from datetime import datetime, timezone
from typing import Optional

import factory
from factory import Factory, Faker

from nga.apps.budgets.domain import Budget, BudgetCalculation
from nga.apps.budgets.domain.dto import BudgetComponentsState
from nga.apps.budgets.enums import BudgetCalculationStatusEnum, BudgetCalculationTypeEnum, BudgetTypeEnum
from nga.core.types import Month
from tests.factories.types import DatePeriodFactory

__all__ = [
    "BudgetFactory",
    "MasterBudgetFactory",
    "FrozenBudgetFactory",
    "BudgetCalculationFactory",
    "BudgetComponentsStateFactory",
]


class BudgetFactory(Factory):
    id = factory.Sequence(lambda n: n)

    name = Faker("pystr")
    description = Faker("pystr")

    period = factory.SubFactory(DatePeriodFactory)

    is_master = False
    type = BudgetTypeEnum.UPDATED

    home_operators = [1]

    active_snapshot_id: int = Faker("pyint")
    calculation_snapshot_id: int = Faker("pyint")

    last_historical_month = factory.LazyAttribute(lambda b: Month.create_from_date(b.period.start_date))

    forecast_rules_modified_at = Faker("date_time", tzinfo=timezone.utc)

    historical_traffic_modified_at = Faker("date_time", tzinfo=timezone.utc)

    agreements_last_modified_at = Faker("date_time", tzinfo=timezone.utc)

    created_at = Faker("date_time", tzinfo=timezone.utc)
    updated_at = Faker("date_time", tzinfo=timezone.utc)

    class Meta:
        model = Budget


class MasterBudgetFactory(BudgetFactory):
    is_master = True
    type = BudgetTypeEnum.MASTER


class FrozenBudgetFactory(BudgetFactory):
    type = BudgetTypeEnum.FROZEN


class BudgetCalculationFactory(factory.Factory):
    id = factory.Sequence(lambda n: n)
    budget_id: int = Faker("pyint")
    budget_snapshot_id: int = Faker("pyint")

    type: BudgetCalculationTypeEnum = BudgetCalculationTypeEnum.FULL_WITH_TRAFFIC_UPDATE

    status: BudgetCalculationStatusEnum = BudgetCalculationStatusEnum.WAITING_FOR_START

    budget_agreement_id: Optional[int] = None

    created_by_user_id: Optional[int] = None

    created_at: datetime = Faker("date_time")
    finished_at: Optional[datetime] = None

    traffic_synchronized_at: Optional[datetime] = None
    forecast_rules_applied_at: Optional[datetime] = None
    agreements_applied_at: Optional[datetime] = None

    budget_lhm = None
    traffic_lhm = None
    distribution_lhm = None

    job_id: Optional[str] = None

    class Meta:
        model = BudgetCalculation


class BudgetComponentsStateFactory(factory.Factory):
    budget_id: int = Faker("pyint")

    historical_traffic_last_modified_at: datetime = Faker("date_time")
    forecast_rules_last_modified_at: datetime = Faker("date_time")

    budget_traffic_synchronized_at: datetime = Faker("date_time")
    forecast_rules_applied_at: datetime = Faker("date_time")

    agreements_last_modified_at: datetime = Faker("date_time")

    budget_lhm = Faker("date")
    traffic_lhm = Faker("date")
    distribution_lhm = Faker("date")

    class Meta:
        model = BudgetComponentsState
