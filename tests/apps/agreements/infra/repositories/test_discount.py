from datetime import date
from decimal import Decimal
from functools import partial
from typing import Optional

import pytest

from nga.apps.agreements.domain.dto import DiscountDTO, DiscountParameterDTO
from nga.apps.agreements.domain.exceptions import DiscountDoesNotExist
from nga.apps.agreements.domain.models import Discount, DiscountParameter
from nga.apps.agreements.domain.models.discount import CommitmentDistributionParameter
from nga.apps.agreements.domain.value_objects import DiscountQualifyingRule
from nga.apps.agreements.enums import (
    DiscountBalancingEnum,
    DiscountBasisEnum,
    DiscountBoundTypeEnum,
    DiscountCalculationTypeEnum,
    DiscountDirectionEnum,
    DiscountModelTypeEnum,
    DiscountQualifyingBasisEnum,
    DiscountSettlementMethodEnum,
    TaxTypeEnum,
)
from nga.apps.agreements.infra.encoders import CommitmentDistributionParameterJSONEncoder
from nga.apps.agreements.infra.orm import models
from nga.apps.agreements.infra.repositories.discount import (
    DiscountDjangoORMRepository,
    from_orm_to_domain_discount,
    from_orm_to_domain_discount_parameter,
)
from nga.apps.common.queryset_utils import to_pk_list
from nga.core.enums import CallDestinationEnum, IMSICountTypeEnum, ServiceTypeEnum, VolumeTypeEnum
from nga.core.types import DatePeriod, Month
from nga.utils.collections import to_id_list
from tests.factories.agreements.domain import (
    CommitmentDistributionParameterFactory,
    DiscountDTOFactory,
    DiscountParameterDTOFactory,
    DiscountQualifyingRuleFactory,
)
from tests.factories.agreements.orm import AgreementORMFactory, DiscountORMFactory, DiscountParameterORMFactory
from tests.factories.references import CountryORMFactory, OperatorORMFactory, TrafficSegmentORMFactory


@pytest.fixture
def _repository() -> DiscountDjangoORMRepository:
    return DiscountDjangoORMRepository()


@pytest.mark.django_db
class TestGetMany:
    def test_filters_by_agreement_id(self, _repository: DiscountDjangoORMRepository):
        # noise discounts
        DiscountORMFactory.create_batch(size=3)

        orm_agreement = AgreementORMFactory()
        orm_discounts = DiscountORMFactory.create_batch(size=2, agreement=orm_agreement)

        domain_discounts = _repository.get_many(agreement_id=orm_agreement.id)

        assert isinstance(domain_discounts, tuple)
        assert len(domain_discounts) == len(orm_discounts)
        assert isinstance(domain_discounts[0], Discount)

        assert sorted(to_id_list(domain_discounts)) == sorted(to_pk_list(orm_discounts))

    def test_filters_by_discount_ids(self, _repository: DiscountDjangoORMRepository):
        orm_agreement = AgreementORMFactory()

        orm_discounts = DiscountORMFactory.create_batch(size=5, agreement=orm_agreement)

        domain_discounts = _repository.get_many(
            agreement_id=orm_agreement.id,
            discount_ids=[d.id for d in orm_discounts[:3]],
        )

        assert sorted(to_id_list(domain_discounts)) == sorted(to_pk_list(orm_discounts[:3]))

    def test_filters_by_directions(self, _repository: DiscountDjangoORMRepository):
        orm_agreement = AgreementORMFactory()

        DiscountORMFactory(agreement=orm_agreement, direction=DiscountDirectionEnum.INBOUND)
        DiscountORMFactory(agreement=orm_agreement, direction=DiscountDirectionEnum.OUTBOUND)
        DiscountORMFactory(agreement=orm_agreement, direction=DiscountDirectionEnum.BIDIRECTIONAL)
        DiscountORMFactory(agreement=orm_agreement, direction=DiscountDirectionEnum.INBOUND)

        domain_discounts = _repository.get_many(
            agreement_id=orm_agreement.id,
            directions=[DiscountDirectionEnum.INBOUND, DiscountDirectionEnum.OUTBOUND],
        )

        assert len(domain_discounts) == 3

    def test_filters_by_service_types(self, _repository: DiscountDjangoORMRepository):
        orm_agreement = AgreementORMFactory()

        discount_factory = partial(DiscountORMFactory, agreement=orm_agreement)

        discount_factory(service_types=tuple([ServiceTypeEnum.VOICE_MO, ServiceTypeEnum.VOICE_MT]))
        discount_factory(service_types=tuple([ServiceTypeEnum.DATA]))
        discount_factory(service_types=tuple([ServiceTypeEnum.VOLTE]))
        discount_factory(service_types=tuple([ServiceTypeEnum.VOICE_MO, ServiceTypeEnum.SMS_MO]))
        discount_factory(service_types=tuple([ServiceTypeEnum.SMS_MT, ServiceTypeEnum.SMS_MO]))  # incorrect

        domain_discounts = _repository.get_many(
            agreement_id=orm_agreement.id,
            service_types=[
                ServiceTypeEnum.VOICE_MO,
                ServiceTypeEnum.VOICE_MT,
                ServiceTypeEnum.DATA,
                ServiceTypeEnum.VOLTE,
            ],
        )

        assert len(domain_discounts) == 4

    def test_filters_by_period(self, _repository: DiscountDjangoORMRepository):
        period = DatePeriod(date(2022, 1, 1), date(2022, 9, 30))
        orm_agreement = AgreementORMFactory()

        discount_factory = partial(DiscountORMFactory, agreement=orm_agreement)

        discount_factory(start_date=date(2021, 12, 1), end_date=date(2022, 1, 31))
        discount1 = discount_factory(start_date=date(2022, 5, 1), end_date=date(2022, 6, 30))
        discount2 = discount_factory(start_date=date(2022, 3, 1), end_date=date(2022, 9, 30))
        discount3 = discount_factory(start_date=date(2022, 1, 1), end_date=date(2022, 8, 31))
        discount_factory(start_date=date(2021, 12, 1), end_date=date(2023, 12, 31))
        discount_factory(start_date=date(2023, 1, 1), end_date=date(2023, 12, 31))

        domain_discounts = _repository.get_many(
            agreement_id=orm_agreement.id,
            period=period,
        )

        assert len(domain_discounts) == 3

        assert sorted(to_id_list(domain_discounts)) == sorted([discount1.id, discount2.id, discount3.id])

    @pytest.mark.parametrize(
        "operators_type",
        [
            "home_operators",
            "partner_operators",
        ],
    )
    def test_filters_by_home_or_partner_operators(self, _repository: DiscountDjangoORMRepository, operators_type):
        orm_agreement = AgreementORMFactory()

        # Noize
        DiscountORMFactory.create_batch(size=5, agreement=orm_agreement, **{operators_type: [OperatorORMFactory()]})

        op_1 = OperatorORMFactory()
        op_2 = OperatorORMFactory()
        op_3 = OperatorORMFactory()

        discount_factory = partial(DiscountORMFactory, agreement=orm_agreement)

        discount1 = discount_factory(**{operators_type: [op_1.id]})
        discount2 = discount_factory(**{operators_type: [op_2.id, op_1.id]})
        discount3 = discount_factory(**{operators_type: [op_2.id, op_3.id]})

        domain_discounts = _repository.get_many(
            agreement_id=orm_agreement.id,
            **{operators_type: [op_1.id, op_2.id, op_3.id]},
        )

        assert len(domain_discounts) == 3

        assert sorted(to_id_list(domain_discounts)) == sorted([discount1.id, discount2.id, discount3.id])

    @pytest.mark.parametrize(
        "home_pmns, partner_pmns, expected_discounts_count",
        [
            (["HPMN1"], None, 1),
            (["HPMN1", "HFAKE"], None, 1),
            (["HFAKE"], None, 0),
            (None, ["PPMN1"], 1),
            (None, ["PPMN1", "PFAKE"], 1),
            (None, ["PFAKE"], 0),
            (["HPMN1"], ["PPMN1"], 1),
            (["HPMN1"], ["PPMN1", "PFAKE"], 1),
            (["HPMN1"], ["PFAKE"], 0),
        ],
    )
    def test_filters_by_home_and_partner_operators(
        self,
        _repository: DiscountDjangoORMRepository,
        home_pmns: Optional[list[str]],
        partner_pmns: Optional[list[str]],
        expected_discounts_count: int,
    ):
        orm_agreement = AgreementORMFactory()

        # Noize
        DiscountORMFactory.create_batch(
            size=3,
            agreement=orm_agreement,
            home_operators=[OperatorORMFactory()],
            partner_operators=[OperatorORMFactory()],
        )

        ho_1 = OperatorORMFactory(pmn_code="HPMN1")
        po_1 = OperatorORMFactory(pmn_code="PPMN1")
        po_2 = OperatorORMFactory(pmn_code="PPMN2")

        searchable_discount = DiscountORMFactory(  # Discount for search
            agreement=orm_agreement,
            home_operators=[ho_1],
            partner_operators=[po_1, po_2],
        )

        operators_map = {
            ho_1.pmn_code: ho_1.id,
            po_1.pmn_code: po_1.id,
            po_2.pmn_code: po_2.id,
        }

        home_operators = [operators_map.get(pmn) for pmn in home_pmns] if home_pmns is not None else None
        partner_operators = [operators_map.get(pmn) for pmn in partner_pmns] if partner_pmns is not None else None

        domain_discounts = _repository.get_many(
            agreement_id=orm_agreement.id,
            home_operators=home_operators,
            partner_operators=partner_operators,
        )

        assert len(domain_discounts) == expected_discounts_count

        if expected_discounts_count:
            assert domain_discounts[0].id == searchable_discount.id

    def test_returns_only_parent_discounts(self, _repository):
        orm_agreement = AgreementORMFactory()

        parent_discount = DiscountORMFactory(parent=None, agreement=orm_agreement)  # parent discount
        DiscountORMFactory(parent=parent_discount, agreement=orm_agreement)  # child discount

        domain_discounts = _repository.get_many(agreement_id=orm_agreement.id)

        assert to_id_list(domain_discounts) == [parent_discount.id]


@pytest.mark.django_db
class TestGetByID:
    def test_when_exists(self, _repository: DiscountDjangoORMRepository):
        orm_discount = DiscountORMFactory()

        domain_discount = _repository.get_by_id(orm_discount.id)

        assert isinstance(domain_discount, Discount)
        assert domain_discount.id == orm_discount.pk

    def test_when_does_not_exist(self, _repository: DiscountDjangoORMRepository):
        with pytest.raises(DiscountDoesNotExist):
            _repository.get_by_id(discount_id=456)


@pytest.mark.django_db
class TestDeleteByID:
    def test_ok(self, _repository: DiscountDjangoORMRepository):
        orm_discount = DiscountORMFactory()
        DiscountParameterORMFactory.create_batch(size=2, discount=orm_discount)

        noise_params = DiscountParameterORMFactory.create_batch(size=2)

        _repository.delete_by_id(orm_discount.pk)

        assert models.Discount.objects.filter(pk=orm_discount.pk).exists() is False
        assert models.DiscountParameter.objects.filter(discount_id=orm_discount.pk).exists() is False

        assert models.DiscountParameter.objects.filter(pk__in=to_pk_list(noise_params)).exists() is True


@pytest.mark.django_db
class TestCreateDiscount:
    def test_discount(self, _repository: DiscountDjangoORMRepository):
        orm_home_operator = OperatorORMFactory()
        orm_partner_operator = OperatorORMFactory()
        orm_country = CountryORMFactory()
        orm_traffic_segment = TrafficSegmentORMFactory()

        commitment_distribution_param = CommitmentDistributionParameterFactory(
            home_operators=tuple([orm_home_operator.id]),
            partner_operators=tuple([orm_partner_operator.id]),
            charge=Decimal("778.59"),
        )

        discount_dto: DiscountDTO = DiscountDTOFactory(
            home_operators=tuple([orm_home_operator.id]),
            partner_operators=tuple([orm_partner_operator.id]),
            direction=DiscountDirectionEnum.INBOUND,
            service_types=tuple([ServiceTypeEnum.DATA]),
            start_date=Month.create_from_year_month(2023, 10),
            end_date=Month.create_from_year_month(2023, 10),
            currency_code="EUR",
            tax_type=TaxTypeEnum.NET,
            volume_type=VolumeTypeEnum.ACTUAL,
            settlement_method=DiscountSettlementMethodEnum.CREDIT_NOTE_EOA,
            called_countries=tuple([orm_country.id]),
            call_destinations=tuple([CallDestinationEnum.HOME]),
            traffic_segments=tuple([orm_traffic_segment.id]),
            imsi_count_type=IMSICountTypeEnum.NO_DATA,
            qualifying_rule=DiscountQualifyingRuleFactory(),
            parameters=tuple([DiscountParameterDTOFactory()]),
            model_type=DiscountModelTypeEnum.SINGLE_RATE_EFFECTIVE,
            above_commitment_rate=Decimal("112.5564"),
            inbound_market_share=Decimal("1234.98"),
            commitment_distribution_parameters=[commitment_distribution_param],
        )
        orm_agreement = AgreementORMFactory()

        domain_discount = _repository.create(orm_agreement.id, discount_dto)

        assert isinstance(domain_discount, Discount)

        assert domain_discount.agreement_id == orm_agreement.id
        assert domain_discount.home_operators == (orm_home_operator.id,)
        assert domain_discount.partner_operators == (orm_partner_operator.id,)
        assert domain_discount.direction == discount_dto.direction
        assert domain_discount.service_types == tuple(domain_discount.service_types)

        assert domain_discount.period == DatePeriod(discount_dto.start_date, discount_dto.end_date)

        assert domain_discount.currency_code == discount_dto.currency_code
        assert domain_discount.tax_type == discount_dto.tax_type
        assert domain_discount.volume_type == discount_dto.volume_type
        assert domain_discount.settlement_method == discount_dto.settlement_method

        assert domain_discount.called_countries == discount_dto.called_countries
        assert domain_discount.call_destinations == discount_dto.call_destinations
        assert domain_discount.traffic_segments == discount_dto.traffic_segments
        assert domain_discount.imsi_count_type == discount_dto.imsi_count_type

        assert domain_discount.above_commitment_rate == discount_dto.above_commitment_rate

        assert domain_discount.inbound_market_share == discount_dto.inbound_market_share

        assert domain_discount.qualifying_rule.direction == discount_dto.qualifying_rule.direction
        assert domain_discount.qualifying_rule.service_types == discount_dto.qualifying_rule.service_types
        assert domain_discount.qualifying_rule.basis == discount_dto.qualifying_rule.basis
        assert domain_discount.qualifying_rule.lower_bound == discount_dto.qualifying_rule.lower_bound
        assert domain_discount.qualifying_rule.upper_bound == discount_dto.qualifying_rule.upper_bound

        assert domain_discount.model_type == DiscountModelTypeEnum.SINGLE_RATE_EFFECTIVE

        assert len(domain_discount.parameters) == 1

        domain_commitment_distribution_parameters = domain_discount.commitment_distribution_parameters
        assert len(domain_commitment_distribution_parameters) == 1

        domain_commitment_distribution_parameter = domain_discount.commitment_distribution_parameters[0]

        assert domain_commitment_distribution_parameter == commitment_distribution_param

    def test_discount_nullable_fields(self, _repository: DiscountDjangoORMRepository):
        orm_home_operator = OperatorORMFactory()
        orm_partner_operator = OperatorORMFactory()

        discount_dto: DiscountDTO = DiscountDTOFactory(
            home_operators=tuple([orm_home_operator.id]),
            partner_operators=tuple([orm_partner_operator.id]),
            called_countries=None,
            call_destinations=None,
            traffic_segments=None,
            imsi_count_type=None,
            qualifying_rule=None,
            model_type=None,
            parameters=tuple([DiscountParameterDTOFactory()]),
        )
        orm_agreement = AgreementORMFactory()

        domain_discount = _repository.create(orm_agreement.id, discount_dto)

        assert domain_discount.called_countries is None
        assert domain_discount.call_destinations is None
        assert domain_discount.traffic_segments is None
        assert domain_discount.imsi_count_type is None
        assert domain_discount.qualifying_rule is None

        assert domain_discount.model_type is None

        assert len(domain_discount.parameters) == 1


@pytest.mark.django_db
class TestCreateDiscountParameters:
    def test_create_parameters(self, _repository: DiscountDjangoORMRepository):
        dto = DiscountParameterDTO(
            calculation_type=DiscountCalculationTypeEnum.SINGLE_RATE_EFFECTIVE,
            basis=DiscountBasisEnum.VALUE,
            basis_value=Decimal("3421.12"),
            balancing=DiscountBalancingEnum.NO_BALANCING,
            bound_type=DiscountBoundTypeEnum.VOLUME,
            lower_bound=None,
            upper_bound=None,
            toll_rate=Decimal("1421.12"),
            airtime_rate=Decimal("2421.12"),
            fair_usage_rate=None,
            fair_usage_threshold=None,
            access_fee_rate=Decimal("34"),
            incremental_rate=Decimal("40"),
        )

        orm_discount = DiscountORMFactory()

        domain_parameters = _repository.create_parameters(orm_discount.pk, tuple([dto]))

        assert len(domain_parameters) == 1

        domain_parameter = domain_parameters[0]

        assert isinstance(domain_parameter, DiscountParameter)

        assert domain_parameter.discount_id == orm_discount.pk
        assert domain_parameter.calculation_type == dto.calculation_type
        assert domain_parameter.basis == dto.basis
        assert domain_parameter.basis_value == dto.basis_value
        assert domain_parameter.balancing == dto.balancing
        assert domain_parameter.bound_type == dto.bound_type
        assert domain_parameter.lower_bound is None
        assert domain_parameter.upper_bound is None
        assert domain_parameter.toll_rate == dto.toll_rate
        assert domain_parameter.airtime_rate == dto.airtime_rate
        assert domain_parameter.fair_usage_rate is None
        assert domain_parameter.fair_usage_threshold is None
        assert domain_parameter.access_fee_rate == dto.access_fee_rate
        assert domain_parameter.incremental_rate == dto.incremental_rate


@pytest.mark.django_db
class TestSaveDiscount:
    def test_save_flat_fields(self, _repository: DiscountDjangoORMRepository):
        orm_discount = DiscountORMFactory(
            direction=DiscountDirectionEnum.INBOUND,
            service_types=[ServiceTypeEnum.DATA],
            currency_code="EUR",
            tax_type=TaxTypeEnum.NET,
            call_destinations=None,
            imsi_count_type=None,
            model_type=None,
        )

        domain_discount = from_orm_to_domain_discount(orm_discount)

        domain_discount.period = DatePeriod(date(2023, 10, 1), date(2023, 11, 1))
        domain_discount.direction = DiscountDirectionEnum.BIDIRECTIONAL
        domain_discount.service_types = tuple([ServiceTypeEnum.VOICE_MO])
        domain_discount.currency_code = "USD"
        domain_discount.tax_type = TaxTypeEnum.GROSS
        domain_discount.imsi_count_type = IMSICountTypeEnum.DATA
        domain_discount.above_commitment_rate = Decimal("556.3456")
        domain_discount.inbound_market_share = Decimal("7777777.97")
        domain_discount.commitment_distribution_parameters = tuple(
            CommitmentDistributionParameterFactory.create_batch(3)
        )

        domain_discount.model_type = DiscountModelTypeEnum.SINGLE_RATE_EFFECTIVE

        updated_domain_discount = _repository.save(domain_discount)

        assert updated_domain_discount == domain_discount

        # not modified fields
        assert updated_domain_discount.volume_type == orm_discount.volume_type
        assert updated_domain_discount.settlement_method == orm_discount.settlement_method
        assert updated_domain_discount.call_destinations == orm_discount.call_destinations
        assert updated_domain_discount.qualifying_rule is None

        orm_discount.refresh_from_db()

        assert DatePeriod(orm_discount.start_date, orm_discount.end_date) == updated_domain_discount.period
        assert orm_discount.direction == updated_domain_discount.direction
        assert orm_discount.service_types == [ServiceTypeEnum.VOICE_MO]
        assert orm_discount.currency_code == updated_domain_discount.currency_code
        assert orm_discount.tax_type == updated_domain_discount.tax_type
        assert orm_discount.imsi_count_type == updated_domain_discount.imsi_count_type
        assert orm_discount.above_commitment_rate == updated_domain_discount.above_commitment_rate
        assert orm_discount.inbound_market_share == updated_domain_discount.inbound_market_share

        assert orm_discount.model_type == updated_domain_discount.model_type

    def test_save_m2m_fields(self, _repository: DiscountDjangoORMRepository):
        ini_hpmn = OperatorORMFactory()
        new_hpmn = OperatorORMFactory()

        ini_called_country = CountryORMFactory()
        new_called_country = CountryORMFactory()

        orm_discount = DiscountORMFactory(home_operators=[ini_hpmn], called_countries=[ini_called_country])

        domain_discount = from_orm_to_domain_discount(orm_discount)

        domain_discount.home_operators = [new_hpmn.pk]
        domain_discount.called_countries = [new_called_country.pk]

        updated_domain_discount = _repository.save(domain_discount)

        assert updated_domain_discount.home_operators == tuple([new_hpmn.pk])
        assert updated_domain_discount.partner_operators == domain_discount.partner_operators
        assert updated_domain_discount.called_countries == tuple([new_called_country.pk])
        assert updated_domain_discount.traffic_segments == domain_discount.traffic_segments

        assert orm_discount.home_operators.count() == 1
        assert orm_discount.home_operators.first() == new_hpmn

        assert orm_discount.called_countries.count() == 1
        assert orm_discount.called_countries.first() == new_called_country


@pytest.mark.django_db
class TestSetParameters:
    def test_set_parameters_instead_of_existing(self, _repository: DiscountDjangoORMRepository):
        orm_parameter_1 = DiscountParameterORMFactory()
        orm_parameter_2 = DiscountParameterORMFactory(discount=orm_parameter_1.discount)

        parameter_dto = DiscountParameterDTOFactory()

        domain_parameters = _repository.set_parameters(orm_parameter_1.discount_id, tuple([parameter_dto]))

        assert models.DiscountParameter.objects.filter(pk=orm_parameter_1.pk).exists() is False
        assert models.DiscountParameter.objects.filter(pk=orm_parameter_2.pk).exists() is False

        new_orm_param = models.DiscountParameter.objects.filter(discount_id=orm_parameter_1.discount_id).first()

        assert len(domain_parameters) == 1
        assert domain_parameters[0] == from_orm_to_domain_discount_parameter(new_orm_param)

        assert new_orm_param.calculation_type == parameter_dto.calculation_type
        assert new_orm_param.basis == parameter_dto.basis
        assert new_orm_param.basis_value == parameter_dto.basis_value
        assert new_orm_param.balancing == parameter_dto.balancing
        assert new_orm_param.bound_type == parameter_dto.bound_type
        assert new_orm_param.lower_bound == parameter_dto.lower_bound
        assert new_orm_param.upper_bound == parameter_dto.upper_bound
        assert new_orm_param.toll_rate == parameter_dto.toll_rate
        assert new_orm_param.airtime_rate == parameter_dto.airtime_rate
        assert new_orm_param.fair_usage_rate == parameter_dto.fair_usage_rate
        assert new_orm_param.fair_usage_threshold == parameter_dto.fair_usage_threshold

        assert models.DiscountParameter.objects.filter(discount_id=orm_parameter_1.discount_id).count() == 1


@pytest.mark.django_db
class TestCreateSubDiscount:
    def test_create(self, _repository: DiscountDjangoORMRepository):
        orm_discount = DiscountORMFactory()

        assert orm_discount.sub_discounts.count() == 0

        orm_home_operator = OperatorORMFactory()
        orm_partner_operator = OperatorORMFactory()
        orm_country = CountryORMFactory()
        orm_traffic_segment = TrafficSegmentORMFactory()

        sub_discount_dto: DiscountDTO = DiscountDTOFactory(
            home_operators=tuple([orm_home_operator.id]),
            partner_operators=tuple([orm_partner_operator.id]),
            direction=DiscountDirectionEnum.INBOUND,
            service_types=tuple([ServiceTypeEnum.DATA]),
            start_date=Month.create_from_year_month(2023, 10),
            end_date=Month.create_from_year_month(2023, 10),
            currency_code="EUR",
            tax_type=TaxTypeEnum.NET,
            volume_type=VolumeTypeEnum.ACTUAL,
            settlement_method=DiscountSettlementMethodEnum.CREDIT_NOTE_EOA,
            called_countries=tuple([orm_country.id]),
            call_destinations=tuple([CallDestinationEnum.HOME]),
            traffic_segments=tuple([orm_traffic_segment.id]),
            imsi_count_type=IMSICountTypeEnum.DATA,
            qualifying_rule=DiscountQualifyingRule(
                direction=DiscountDirectionEnum.BIDIRECTIONAL,
                service_types=tuple([ServiceTypeEnum.VOICE_MT]),
                volume_type=VolumeTypeEnum.ACTUAL,
                basis=DiscountQualifyingBasisEnum.VOLUME,
                lower_bound=Decimal("15"),
                upper_bound=Decimal("2000"),
            ),
            inbound_market_share=Decimal("785.82"),
            parameters=tuple([DiscountParameterDTOFactory()]),
        )

        domain_sub_discount = _repository.create_sub_discount(orm_discount.id, sub_discount_dto)
        assert isinstance(domain_sub_discount, Discount)

        assert orm_discount.sub_discounts.count() == 1
        orm_sub_discount = orm_discount.sub_discounts.first()

        assert domain_sub_discount.id == orm_sub_discount.pk

        assert orm_sub_discount.agreement_id == orm_discount.agreement_id

        assert to_pk_list(orm_sub_discount.home_operators.all()) == [orm_home_operator.id]
        assert to_pk_list(orm_sub_discount.partner_operators.all()) == [orm_partner_operator.id]
        assert orm_sub_discount.direction == sub_discount_dto.direction
        assert orm_sub_discount.service_types == list(sub_discount_dto.service_types)

        assert orm_sub_discount.start_date == sub_discount_dto.start_date
        assert orm_sub_discount.end_date == sub_discount_dto.end_date

        assert orm_sub_discount.currency_code == sub_discount_dto.currency_code
        assert orm_sub_discount.tax_type == sub_discount_dto.tax_type
        assert orm_sub_discount.volume_type == sub_discount_dto.volume_type
        assert orm_sub_discount.settlement_method == sub_discount_dto.settlement_method
        assert to_pk_list(orm_sub_discount.called_countries.all()) == list(sub_discount_dto.called_countries)
        assert orm_sub_discount.call_destinations == list(sub_discount_dto.call_destinations)
        assert to_pk_list(orm_sub_discount.traffic_segments.all()) == list(sub_discount_dto.traffic_segments)

        assert orm_sub_discount.imsi_count_type == sub_discount_dto.imsi_count_type

        assert orm_sub_discount.inbound_market_share == sub_discount_dto.inbound_market_share

        assert orm_sub_discount.qualifying_direction == sub_discount_dto.qualifying_rule.direction
        assert orm_sub_discount.qualifying_service_types == list(sub_discount_dto.qualifying_rule.service_types)
        assert orm_sub_discount.qualifying_basis == sub_discount_dto.qualifying_rule.basis
        assert orm_sub_discount.qualifying_lower_bound == sub_discount_dto.qualifying_rule.lower_bound
        assert orm_sub_discount.qualifying_upper_bound == sub_discount_dto.qualifying_rule.upper_bound

        assert orm_sub_discount.parameters.count() == 1


@pytest.mark.django_db
class TestHasIntersectionWithinAgreement:
    def test_only_parent_discounts_are_verified(self, _repository: DiscountDjangoORMRepository):
        orm_discount = DiscountORMFactory(call_destinations=None, traffic_segments=[], called_countries=[])

        DiscountORMFactory(  # sub-discount
            agreement=orm_discount.agreement,
            home_operators=orm_discount.home_operators.all(),
            partner_operators=orm_discount.partner_operators.all(),
            direction=orm_discount.direction,
            service_types=orm_discount.service_types,
            start_date=orm_discount.start_date,
            end_date=orm_discount.end_date,
            parent=orm_discount,
        )

        domain_discount = from_orm_to_domain_discount(orm_discount)

        has_intersection = _repository.has_intersection_within_agreement(domain_discount)

        assert has_intersection is False

    @pytest.mark.parametrize(
        "first_discount_cd, first_discount_cc, second_discount_cd, second_discount_cc, expected_result",
        [
            (None, None, [CallDestinationEnum.HOME], None, True),
            (None, None, None, ["AAA"], True),
            (None, ["BBB"], None, ["AAA"], False),
            (None, None, None, None, True),
            ([CallDestinationEnum.HOME], None, None, ["AAA"], False),
            (None, ["AAA"], [CallDestinationEnum.HOME], None, False),
            ([CallDestinationEnum.HOME], None, [CallDestinationEnum.HOME, CallDestinationEnum.LOCAL], None, True),
            (
                [CallDestinationEnum.HOME, CallDestinationEnum.LOCAL],
                None,
                [CallDestinationEnum.INTERNATIONAL],
                None,
                False,
            ),
            (None, ["AAA"], None, ["AAA", "CCC"], True),
            (None, ["AAA"], None, ["BBB", "CCC"], False),
        ],
    )
    def test_intersected_with_call_destination_and_called_countries_fields(
        self,
        _repository: DiscountDjangoORMRepository,
        first_discount_cd: Optional[list[CallDestinationEnum]],
        first_discount_cc: Optional[list[str]],
        second_discount_cd: Optional[list[CallDestinationEnum]],
        second_discount_cc: Optional[list[str]],
        expected_result: bool,
    ):
        called_countries = [
            CountryORMFactory(code="AAA"),
            CountryORMFactory(code="BBB"),
            CountryORMFactory(code="CCC"),
        ]

        if first_discount_cc is not None:
            first_discount_cc = [c for c in called_countries if c.code in first_discount_cc]

        if second_discount_cc is not None:
            second_discount_cc = [c for c in called_countries if c.code in second_discount_cc]

        parameters = dict(
            agreement=AgreementORMFactory(),
            parent=None,
            home_operators=[OperatorORMFactory()],
            partner_operators=[OperatorORMFactory()],
            start_date=date(2023, 10, 1),
            end_date=date(2023, 11, 1),
            direction=DiscountDirectionEnum.INBOUND,
            service_types=[ServiceTypeEnum.VOICE_MO],
            traffic_segments=[TrafficSegmentORMFactory()],
        )

        # First Discount
        DiscountORMFactory(
            **{**parameters, "call_destinations": first_discount_cd, "called_countries": first_discount_cc}
        )

        second_discount = DiscountORMFactory(
            **{**parameters, "call_destinations": second_discount_cd, "called_countries": second_discount_cc}
        )

        domain_discount = from_orm_to_domain_discount(second_discount)

        has_intersection = _repository.has_intersection_within_agreement(domain_discount)

        assert has_intersection == expected_result

    def test_not_intersected_when_all_parameters_are_set(self, _repository: DiscountDjangoORMRepository):
        parameters = dict(
            agreement=AgreementORMFactory(),
            home_operators=[OperatorORMFactory()],
            partner_operators=[OperatorORMFactory()],
            start_date=date(2023, 10, 1),
            end_date=date(2023, 11, 1),
            direction=DiscountDirectionEnum.INBOUND,
            service_types=[ServiceTypeEnum.VOICE_MO],
            call_destinations=[CallDestinationEnum.HOME, CallDestinationEnum.LOCAL],
            called_countries=None,
            traffic_segments=[TrafficSegmentORMFactory()],
        )

        orm_verifying_discount = DiscountORMFactory(**parameters)

        DiscountORMFactory(**{**parameters, "agreement": AgreementORMFactory()})
        DiscountORMFactory(**{**parameters, "home_operators": [OperatorORMFactory()]})
        DiscountORMFactory(**{**parameters, "partner_operators": [OperatorORMFactory()]})
        DiscountORMFactory(**{**parameters, "start_date": date(2023, 8, 1), "end_date": date(2023, 8, 1)})
        DiscountORMFactory(**{**parameters, "start_date": date(2023, 12, 1), "end_date": date(2023, 12, 1)})
        DiscountORMFactory(**{**parameters, "direction": DiscountDirectionEnum.OUTBOUND})
        DiscountORMFactory(**{**parameters, "service_types": [ServiceTypeEnum.DATA]})
        DiscountORMFactory(**{**parameters, "call_destinations": [CallDestinationEnum.INTERNATIONAL]})
        DiscountORMFactory(**{**parameters, "call_destinations": None, "called_countries": [CountryORMFactory()]})
        DiscountORMFactory(**{**parameters, "traffic_segments": [TrafficSegmentORMFactory()]})

        domain_discount = from_orm_to_domain_discount(orm_verifying_discount)

        has_intersection = _repository.has_intersection_within_agreement(domain_discount)

        assert has_intersection is False

    def test_service_types_intersection(self, _repository: DiscountDjangoORMRepository):
        parameters = dict(
            agreement=AgreementORMFactory(),
            home_operators=[OperatorORMFactory()],
            partner_operators=[OperatorORMFactory()],
            start_date=date(2023, 10, 1),
            end_date=date(2023, 11, 1),
            direction=DiscountDirectionEnum.INBOUND,
            service_types=[ServiceTypeEnum.VOICE_MO],
            call_destinations=[CallDestinationEnum.HOME, CallDestinationEnum.LOCAL],
            called_countries=[CountryORMFactory()],
            traffic_segments=[TrafficSegmentORMFactory()],
        )

        orm_verifying_discount = DiscountORMFactory(**parameters)

        DiscountORMFactory(
            **{
                **parameters,
                "service_types": [ServiceTypeEnum.VOICE_MT, ServiceTypeEnum.DATA, ServiceTypeEnum.VOICE_MO],
            }
        )

        domain_discount = from_orm_to_domain_discount(orm_verifying_discount)

        has_intersection = _repository.has_intersection_within_agreement(domain_discount)

        assert has_intersection is True

    def test_intersection_does_not_exist_when_required_fields_differ(self, _repository: DiscountDjangoORMRepository):
        parameters = dict(
            agreement=AgreementORMFactory(),
            home_operators=[OperatorORMFactory()],
            partner_operators=[OperatorORMFactory()],
            start_date=date(2023, 10, 1),
            end_date=date(2023, 11, 1),
            direction=DiscountDirectionEnum.INBOUND,
            service_types=[ServiceTypeEnum.VOICE_MO],
            call_destinations=[CallDestinationEnum.HOME],
            called_countries=[CountryORMFactory()],
            traffic_segments=[],
        )
        DiscountORMFactory(**{**parameters, "direction": DiscountDirectionEnum.OUTBOUND})

        orm_verifying_discount = DiscountORMFactory(**parameters)

        domain_discount = from_orm_to_domain_discount(orm_verifying_discount)

        has_intersection = _repository.has_intersection_within_agreement(domain_discount)

        assert has_intersection is False

    def test_intersection_does_not_exist_when_nullable_fields_are_changing(
        self,
        _repository: DiscountDjangoORMRepository,
    ):
        parameters = dict(
            agreement=AgreementORMFactory(),
            home_operators=[OperatorORMFactory()],
            partner_operators=[OperatorORMFactory()],
            start_date=date(2023, 10, 1),
            end_date=date(2023, 11, 1),
            direction=DiscountDirectionEnum.INBOUND,
            service_types=[ServiceTypeEnum.VOICE_MO],
            call_destinations=[CallDestinationEnum.HOME],
            called_countries=[CountryORMFactory()],
            traffic_segments=[],
        )
        DiscountORMFactory(
            **{
                **parameters,
                "call_destinations": [CallDestinationEnum.LOCAL],
                "called_countries": [],
                "traffic_segments": [TrafficSegmentORMFactory(), TrafficSegmentORMFactory()],
            }
        )

        orm_verifying_discount = DiscountORMFactory(**parameters)

        domain_discount = from_orm_to_domain_discount(orm_verifying_discount)

        has_intersection = _repository.has_intersection_within_agreement(domain_discount)

        assert has_intersection is False

    @pytest.mark.parametrize(
        "checked_cds,existing_cds",
        [
            (None, None),
            (None, [CallDestinationEnum.HOME]),
            ([CallDestinationEnum.HOME], None),
            ([CallDestinationEnum.HOME], [CallDestinationEnum.HOME]),
        ],
    )
    def test_has_intersection_when_call_destinations_are_empty(
        self,
        # test parameters
        checked_cds: list[CallDestinationEnum],
        existing_cds: list[CallDestinationEnum],
        # fixtures
        _repository: DiscountDjangoORMRepository,
    ):
        parameters = dict(
            agreement=AgreementORMFactory(),
            home_operators=[OperatorORMFactory()],
            partner_operators=[OperatorORMFactory()],
            start_date=date(2023, 10, 1),
            end_date=date(2023, 11, 1),
            direction=DiscountDirectionEnum.INBOUND,
            service_types=[ServiceTypeEnum.VOICE_MT, ServiceTypeEnum.VOICE_MO],
            call_destinations=checked_cds,
            called_countries=None,
            traffic_segments=[TrafficSegmentORMFactory()],
        )

        orm_verifying_discount = DiscountORMFactory(**parameters)

        DiscountORMFactory(**{**parameters, "call_destinations": existing_cds})

        domain_discount = from_orm_to_domain_discount(orm_verifying_discount)

        has_intersection = _repository.has_intersection_within_agreement(domain_discount)

        assert has_intersection is True

    @pytest.mark.parametrize(
        "checked_has_value,existing_has_value",
        [
            (None, None),
            (None, True),
            (True, None),
            (True, True),
        ],
    )
    def test_has_intersection_when_called_countries_are_empty(
        self,
        # test parameters
        checked_has_value: Optional[bool],
        existing_has_value: Optional[bool],
        # fixtures
        _repository: DiscountDjangoORMRepository,
    ):
        country = CountryORMFactory()

        parameters = dict(
            agreement=AgreementORMFactory(),
            home_operators=[OperatorORMFactory()],
            partner_operators=[OperatorORMFactory()],
            start_date=date(2023, 10, 1),
            end_date=date(2023, 11, 1),
            direction=DiscountDirectionEnum.INBOUND,
            service_types=[ServiceTypeEnum.VOICE_MT, ServiceTypeEnum.VOICE_MO],
            call_destinations=[CallDestinationEnum.HOME],
            called_countries=[country] if checked_has_value else None,
            traffic_segments=[TrafficSegmentORMFactory()],
        )

        orm_verifying_discount = DiscountORMFactory(**parameters)

        DiscountORMFactory(**{**parameters, "called_countries": [country] if existing_has_value else None})

        domain_discount = from_orm_to_domain_discount(orm_verifying_discount)

        has_intersection = _repository.has_intersection_within_agreement(domain_discount)

        assert has_intersection is True

    @pytest.mark.parametrize(
        "checked_has_value,existing_has_value",
        [
            (None, None),
            (None, True),
            (True, None),
            (True, True),
        ],
    )
    def test_has_intersection_when_traffic_segments_are_empty(
        self,
        # test parameters
        checked_has_value: Optional[bool],
        existing_has_value: Optional[bool],
        # fixtures
        _repository: DiscountDjangoORMRepository,
    ):
        segment = TrafficSegmentORMFactory()

        parameters = dict(
            agreement=AgreementORMFactory(),
            home_operators=[OperatorORMFactory()],
            partner_operators=[OperatorORMFactory()],
            start_date=date(2023, 10, 1),
            end_date=date(2023, 11, 1),
            direction=DiscountDirectionEnum.INBOUND,
            service_types=[ServiceTypeEnum.VOICE_MT, ServiceTypeEnum.VOICE_MO],
            call_destinations=[CallDestinationEnum.HOME],
            called_countries=[CountryORMFactory()],
            traffic_segments=[segment] if checked_has_value else None,
        )

        orm_verifying_discount = DiscountORMFactory(**parameters)

        DiscountORMFactory(**{**parameters, "traffic_segments": [segment] if existing_has_value else None})

        domain_discount = from_orm_to_domain_discount(orm_verifying_discount)

        has_intersection = _repository.has_intersection_within_agreement(domain_discount)

        assert has_intersection is True

    @pytest.mark.parametrize(
        "checked,existing",
        [
            (DiscountDirectionEnum.INBOUND, DiscountDirectionEnum.INBOUND),
            (DiscountDirectionEnum.OUTBOUND, DiscountDirectionEnum.OUTBOUND),
            (DiscountDirectionEnum.INBOUND, DiscountDirectionEnum.BIDIRECTIONAL),
            (DiscountDirectionEnum.OUTBOUND, DiscountDirectionEnum.BIDIRECTIONAL),
            (DiscountDirectionEnum.BIDIRECTIONAL, DiscountDirectionEnum.OUTBOUND),
            (DiscountDirectionEnum.BIDIRECTIONAL, DiscountDirectionEnum.INBOUND),
        ],
    )
    def test_has_intersection_by_direction(
        self,
        # test parameters
        checked: DiscountDirectionEnum,
        existing: DiscountDirectionEnum,
        # fixtures
        _repository: DiscountDjangoORMRepository,
    ):
        parameters = dict(
            agreement=AgreementORMFactory(),
            home_operators=[OperatorORMFactory()],
            partner_operators=[OperatorORMFactory()],
            start_date=date(2023, 10, 1),
            end_date=date(2023, 11, 1),
            direction=checked,
            service_types=[ServiceTypeEnum.VOICE_MT, ServiceTypeEnum.VOICE_MO],
            call_destinations=[CallDestinationEnum.HOME],
            called_countries=[CountryORMFactory()],
            traffic_segments=[TrafficSegmentORMFactory()],
        )

        orm_verifying_discount = DiscountORMFactory(**parameters)

        DiscountORMFactory(**{**parameters, "direction": existing})

        domain_discount = from_orm_to_domain_discount(orm_verifying_discount)

        has_intersection = _repository.has_intersection_within_agreement(domain_discount)

        assert has_intersection is True

    def test_discount_is_intersected_when_discount_without_cds_exists(self, _repository):
        existing_orm_discount = DiscountORMFactory(
            agreement=AgreementORMFactory(),
            home_operators=[OperatorORMFactory()],
            partner_operators=[OperatorORMFactory()],
            start_date=date(2023, 10, 1),
            end_date=date(2023, 11, 1),
            direction=DiscountDirectionEnum.INBOUND,
            service_types=[ServiceTypeEnum.VOICE_MT, ServiceTypeEnum.VOICE_MO],
            call_destinations=[],
            called_countries=[],
            traffic_segments=[],
        )

        orm_verifying_discount = DiscountORMFactory(
            agreement=existing_orm_discount.agreement,
            home_operators=existing_orm_discount.home_operators.all(),
            partner_operators=existing_orm_discount.partner_operators.all(),
            start_date=date(2023, 10, 1),
            end_date=date(2023, 11, 1),
            direction=DiscountDirectionEnum.INBOUND,
            service_types=[ServiceTypeEnum.VOICE_MT, ServiceTypeEnum.VOICE_MO],
            call_destinations=[CallDestinationEnum.HOME],
            called_countries=[],
            traffic_segments=[],
        )

        domain_discount = from_orm_to_domain_discount(orm_verifying_discount)

        has_intersection = _repository.has_intersection_within_agreement(domain_discount)

        assert has_intersection is True

    def test_has_intersection_when_qualifying_rules_are_empty(
        self,
        _repository: DiscountDjangoORMRepository,
    ):

        parameters = dict(
            agreement=AgreementORMFactory(),
            home_operators=[OperatorORMFactory()],
            partner_operators=[OperatorORMFactory()],
            start_date=date(2023, 10, 1),
            end_date=date(2023, 11, 1),
            direction=DiscountDirectionEnum.INBOUND,
            service_types=[ServiceTypeEnum.VOICE_MT, ServiceTypeEnum.VOICE_MO],
            call_destinations=[CallDestinationEnum.HOME],
            called_countries=[CountryORMFactory()],
            traffic_segments=[TrafficSegmentORMFactory()],
            qualifying_direction=None,
            qualifying_service_types=None,
            qualifying_basis=None,
            qualifying_lower_bound=None,
            qualifying_upper_bound=None,
        )

        orm_verifying_discount = DiscountORMFactory(**parameters)

        DiscountORMFactory(**parameters)

        domain_discount = from_orm_to_domain_discount(orm_verifying_discount)

        has_intersection = _repository.has_intersection_within_agreement(domain_discount)

        assert has_intersection is True

    def test_has_intersection_with_qualifying_rules_and_saved_discount_with_empty_qualifying_rule(
        self,
        _repository: DiscountDjangoORMRepository,
    ):

        parameters = dict(
            agreement=AgreementORMFactory(),
            home_operators=[OperatorORMFactory()],
            partner_operators=[OperatorORMFactory()],
            start_date=date(2023, 10, 1),
            end_date=date(2023, 11, 1),
            direction=DiscountDirectionEnum.INBOUND,
            service_types=[ServiceTypeEnum.VOICE_MT, ServiceTypeEnum.VOICE_MO],
            call_destinations=[CallDestinationEnum.HOME],
            called_countries=[CountryORMFactory()],
            traffic_segments=[TrafficSegmentORMFactory()],
        )

        orm_verifying_discount = DiscountORMFactory(
            **parameters,
            qualifying_direction=DiscountDirectionEnum.INBOUND,
            qualifying_service_types=[ServiceTypeEnum.SMS_MT],
            qualifying_basis=DiscountQualifyingBasisEnum.VOLUME,
            qualifying_lower_bound=Decimal("0"),
            qualifying_upper_bound=Decimal("200"),
        )

        DiscountORMFactory(
            **parameters,
            qualifying_direction=None,
            qualifying_service_types=None,
            qualifying_basis=None,
            qualifying_lower_bound=None,
            qualifying_upper_bound=None,
        )

        domain_discount = from_orm_to_domain_discount(orm_verifying_discount)

        has_intersection = _repository.has_intersection_within_agreement(domain_discount)

        assert has_intersection is True

    @pytest.mark.parametrize(
        "lower_bound, upper_bound, expected_result",
        [
            # In the middle
            (Decimal("40"), Decimal("50"), False),
            (Decimal("35"), Decimal("52"), True),
            # Intersects from right
            (Decimal("40"), Decimal("55"), True),
            (Decimal("40"), None, True),
            # Intersects from left
            (Decimal("0"), Decimal("20"), False),
            (Decimal("0"), Decimal("30"), True),
            (Decimal("20"), Decimal("50"), True),
            # Over full period
            (Decimal("20"), None, True),
            (Decimal("20"), Decimal("70"), True),
            (Decimal("10"), Decimal("50"), True),
            (Decimal("45"), None, True),
        ],
    )
    def test_has_intersection_with_qualifying_rule(
        self,
        _repository: DiscountDjangoORMRepository,
        lower_bound: Decimal,
        upper_bound: Decimal,
        expected_result: bool,
    ):

        parameters = dict(
            agreement=AgreementORMFactory(),
            home_operators=[OperatorORMFactory()],
            partner_operators=[OperatorORMFactory()],
            start_date=date(2023, 10, 1),
            end_date=date(2023, 11, 1),
            direction=DiscountDirectionEnum.INBOUND,
            service_types=[ServiceTypeEnum.VOICE_MT, ServiceTypeEnum.VOICE_MO],
            call_destinations=[CallDestinationEnum.HOME],
            called_countries=[CountryORMFactory()],
            traffic_segments=[TrafficSegmentORMFactory()],
            qualifying_direction=DiscountDirectionEnum.INBOUND,
            qualifying_service_types=[ServiceTypeEnum.SMS_MT],
            qualifying_basis=DiscountQualifyingBasisEnum.VOLUME,
        )

        orm_verifying_discount = DiscountORMFactory(
            **parameters,
            qualifying_lower_bound=lower_bound,
            qualifying_upper_bound=upper_bound,
        )

        DiscountORMFactory(
            **parameters,
            qualifying_lower_bound=Decimal("20"),
            qualifying_upper_bound=Decimal("40"),
        )

        DiscountORMFactory(
            **parameters,
            qualifying_lower_bound=Decimal("50"),
            qualifying_upper_bound=None,
        )

        domain_discount = from_orm_to_domain_discount(orm_verifying_discount)

        has_intersection = _repository.has_intersection_within_agreement(domain_discount)

        assert has_intersection == expected_result

    @pytest.mark.parametrize(
        "lower_bound, upper_bound, expected_result",
        [
            # In the middle
            (Decimal("25"), Decimal("35"), True),
            (Decimal("20"), Decimal("40"), True),
            # Intersects from right
            (Decimal("30"), Decimal("50"), True),
            (Decimal("40"), Decimal("50"), False),
            (Decimal("39"), None, True),
            (Decimal("40"), None, False),
            # Intersects from left
            (Decimal("0"), Decimal("40"), True),
            (Decimal("0"), Decimal("30"), True),
            (Decimal("0"), Decimal("20"), False),
            # Over full period
            (Decimal("0"), Decimal("100"), True),
            (Decimal("20"), Decimal("70"), True),
            (Decimal("0"), Decimal("40"), True),
            (Decimal("0"), None, True),
            (Decimal("20"), None, True),
        ],
    )
    def test_has_intersection_with_qualifying_rule_fields_and_upper_bound_is_no_null(
        self,
        _repository: DiscountDjangoORMRepository,
        lower_bound: Decimal,
        upper_bound: Decimal,
        expected_result: bool,
    ):

        parameters = dict(
            agreement=AgreementORMFactory(),
            home_operators=[OperatorORMFactory()],
            partner_operators=[OperatorORMFactory()],
            start_date=date(2023, 10, 1),
            end_date=date(2023, 11, 1),
            direction=DiscountDirectionEnum.INBOUND,
            service_types=[ServiceTypeEnum.VOICE_MT, ServiceTypeEnum.VOICE_MO],
            call_destinations=[CallDestinationEnum.HOME],
            called_countries=[CountryORMFactory()],
            traffic_segments=[TrafficSegmentORMFactory()],
            qualifying_direction=DiscountDirectionEnum.INBOUND,
            qualifying_service_types=[ServiceTypeEnum.SMS_MT],
            qualifying_basis=DiscountQualifyingBasisEnum.VOLUME,
        )

        orm_verifying_discount = DiscountORMFactory(
            **parameters,
            qualifying_lower_bound=lower_bound,
            qualifying_upper_bound=upper_bound,
        )

        DiscountORMFactory(
            **parameters,
            qualifying_lower_bound=Decimal("20"),
            qualifying_upper_bound=Decimal("40"),
        )

        domain_discount = from_orm_to_domain_discount(orm_verifying_discount)

        has_intersection = _repository.has_intersection_within_agreement(domain_discount)

        assert has_intersection == expected_result

    @pytest.mark.parametrize(
        "lower_bound, upper_bound, expected_result",
        [
            # In the middle
            (Decimal("20"), Decimal("70"), True),
            (Decimal("50"), Decimal("100"), True),
            # Intersects from right
            (Decimal("10"), Decimal("50"), True),
            (Decimal("20"), Decimal("50"), True),
            (Decimal("30"), None, True),
            (Decimal("10"), None, True),
            (Decimal("0"), Decimal("100"), True),
            # Intersects from left
            (Decimal("0"), Decimal("20"), False),
            (Decimal("0"), Decimal("25"), True),
            # Over full period
            (Decimal("0"), None, True),
            (Decimal("20"), None, True),
        ],
    )
    def test_has_intersection_with_qualifying_rule_fields_and_upper_bound_is_null(
        self,
        _repository: DiscountDjangoORMRepository,
        lower_bound: Decimal,
        upper_bound: Decimal,
        expected_result: bool,
    ):

        parameters = dict(
            agreement=AgreementORMFactory(),
            home_operators=[OperatorORMFactory()],
            partner_operators=[OperatorORMFactory()],
            start_date=date(2023, 10, 1),
            end_date=date(2023, 11, 1),
            direction=DiscountDirectionEnum.INBOUND,
            service_types=[ServiceTypeEnum.VOICE_MT, ServiceTypeEnum.VOICE_MO],
            call_destinations=[CallDestinationEnum.HOME],
            called_countries=[CountryORMFactory()],
            traffic_segments=[TrafficSegmentORMFactory()],
            qualifying_direction=DiscountDirectionEnum.INBOUND,
            qualifying_service_types=[ServiceTypeEnum.SMS_MT],
            qualifying_basis=DiscountQualifyingBasisEnum.VOLUME,
        )

        orm_verifying_discount = DiscountORMFactory(
            **parameters,
            qualifying_lower_bound=lower_bound,
            qualifying_upper_bound=upper_bound,
        )

        DiscountORMFactory(
            **parameters,
            qualifying_lower_bound=Decimal("20"),
            qualifying_upper_bound=None,
        )

        domain_discount = from_orm_to_domain_discount(orm_verifying_discount)

        has_intersection = _repository.has_intersection_within_agreement(domain_discount)

        assert has_intersection == expected_result


@pytest.mark.django_db
class TestHasIntersectionBetweenChildDiscounts:
    @pytest.mark.parametrize(
        "first_discount_cd, first_discount_cc, second_discount_cd, second_discount_cc, expected_result",
        [
            (None, None, [CallDestinationEnum.HOME], None, True),
            (None, None, None, ["AAA"], True),
            (None, ["BBB"], None, ["AAA"], False),
            (None, None, None, None, True),
            ([CallDestinationEnum.HOME], None, None, ["AAA"], False),
            (None, ["AAA"], [CallDestinationEnum.HOME], None, False),
            ([CallDestinationEnum.HOME], None, [CallDestinationEnum.HOME, CallDestinationEnum.LOCAL], None, True),
            (
                [CallDestinationEnum.HOME, CallDestinationEnum.LOCAL],
                None,
                [CallDestinationEnum.INTERNATIONAL],
                None,
                False,
            ),
            (None, ["AAA"], None, ["AAA", "CCC"], True),
            (None, ["AAA"], None, ["BBB", "CCC"], False),
        ],
    )
    def test_intersected_with_call_destination_and_called_countries_fields(
        self,
        _repository: DiscountDjangoORMRepository,
        first_discount_cd: Optional[list[CallDestinationEnum]],
        first_discount_cc: Optional[list[str]],
        second_discount_cd: Optional[list[CallDestinationEnum]],
        second_discount_cc: Optional[list[str]],
        expected_result: bool,
    ):
        called_countries = [
            CountryORMFactory(code="AAA"),
            CountryORMFactory(code="BBB"),
            CountryORMFactory(code="CCC"),
        ]

        if first_discount_cc is not None:
            first_discount_cc = [c for c in called_countries if c.code in first_discount_cc]

        if second_discount_cc is not None:
            second_discount_cc = [c for c in called_countries if c.code in second_discount_cc]

        parameters = dict(
            agreement=AgreementORMFactory(),
            home_operators=[OperatorORMFactory()],
            partner_operators=[OperatorORMFactory()],
            start_date=date(2023, 10, 1),
            end_date=date(2023, 11, 1),
            direction=DiscountDirectionEnum.INBOUND,
            service_types=[ServiceTypeEnum.VOICE_MO],
            traffic_segments=[TrafficSegmentORMFactory()],
        )

        orm_verifying_discount = DiscountORMFactory(**parameters)

        child_discount_factory = partial(DiscountORMFactory, parent=orm_verifying_discount)

        # First Discount
        child_discount_factory(
            **{**parameters, "call_destinations": first_discount_cd, "called_countries": first_discount_cc}
        )

        # Second Discount
        child_discount_factory(
            **{**parameters, "call_destinations": second_discount_cd, "called_countries": second_discount_cc}
        )

        domain_discount = from_orm_to_domain_discount(orm_verifying_discount)

        has_intersection = _repository.has_intersection_between_sub_discounts(domain_discount)

        assert has_intersection == expected_result

    def test_not_intersected_when_all_parameters_are_set(self, _repository: DiscountDjangoORMRepository):
        parameters = dict(
            agreement=AgreementORMFactory(),
            home_operators=[OperatorORMFactory()],
            partner_operators=[OperatorORMFactory()],
            start_date=date(2023, 10, 1),
            end_date=date(2023, 11, 1),
            direction=DiscountDirectionEnum.INBOUND,
            service_types=[ServiceTypeEnum.VOICE_MO],
            call_destinations=[CallDestinationEnum.HOME, CallDestinationEnum.LOCAL],
            called_countries=None,
            traffic_segments=[TrafficSegmentORMFactory()],
        )

        orm_verifying_discount = DiscountORMFactory(**parameters)

        child_discount_factory = partial(DiscountORMFactory, parent=orm_verifying_discount)

        child_discount_factory(**{**parameters, "agreement": AgreementORMFactory()})
        child_discount_factory(**{**parameters, "home_operators": [OperatorORMFactory()]})
        child_discount_factory(**{**parameters, "partner_operators": [OperatorORMFactory()]})
        child_discount_factory(**{**parameters, "start_date": date(2023, 8, 1), "end_date": date(2023, 8, 1)})
        child_discount_factory(**{**parameters, "start_date": date(2023, 12, 1), "end_date": date(2023, 12, 1)})
        child_discount_factory(**{**parameters, "direction": DiscountDirectionEnum.OUTBOUND})
        child_discount_factory(**{**parameters, "service_types": [ServiceTypeEnum.DATA]})
        child_discount_factory(**{**parameters, "call_destinations": [CallDestinationEnum.INTERNATIONAL]})
        child_discount_factory(**{**parameters, "call_destinations": None, "called_countries": [CountryORMFactory()]})
        child_discount_factory(**{**parameters, "traffic_segments": [TrafficSegmentORMFactory()]})

        domain_discount = from_orm_to_domain_discount(orm_verifying_discount)

        has_intersection = _repository.has_intersection_between_sub_discounts(domain_discount)

        assert has_intersection is False


@pytest.mark.django_db
class TestDeleteMany:
    def test_delete_many(self, _repository):
        agreement = AgreementORMFactory()
        d1 = DiscountORMFactory(agreement=agreement)
        d2 = DiscountORMFactory(agreement=agreement)
        d3 = DiscountORMFactory(agreement=agreement)

        d4 = DiscountORMFactory()
        d5 = DiscountORMFactory()

        _repository.delete_many(agreement.id)

        assert models.Discount.objects.filter(pk__in=to_id_list([d1, d2, d3])).exists() is False

        assert models.Discount.objects.filter(pk__in=to_id_list([d4, d5])).exists() is True


@pytest.mark.django_db
class TestFromORMToDomainDiscount:
    def test_with_all_values(self):
        country = CountryORMFactory()
        traffic_segment = TrafficSegmentORMFactory()

        parameter = CommitmentDistributionParameter(
            home_operators=(55,),
            partner_operators=(345,),
            charge=Decimal("100.0"),
        )

        orm_discount = DiscountORMFactory(
            tax_type=TaxTypeEnum.NET,
            volume_type=VolumeTypeEnum.ACTUAL,
            settlement_method=DiscountSettlementMethodEnum.CREDIT_NOTE_EOA,
            call_destinations=tuple([CallDestinationEnum.HOME]),
            qualifying_direction=DiscountDirectionEnum.OUTBOUND,
            qualifying_service_types=tuple([ServiceTypeEnum.DATA]),
            qualifying_basis=DiscountQualifyingBasisEnum.VOLUME,
            qualifying_lower_bound=Decimal("0"),
            qualifying_upper_bound=Decimal("100"),
            called_countries=[country],
            traffic_segments=[traffic_segment.id],
            imsi_count_type=IMSICountTypeEnum.NO_DATA,
            model_type=DiscountModelTypeEnum.SINGLE_RATE_EFFECTIVE,
            inbound_market_share=Decimal("345"),
            commitment_distribution_parameters=CommitmentDistributionParameterJSONEncoder.encode_many([parameter]),
        )

        orm_agreement = orm_discount.agreement

        domain_discount = from_orm_to_domain_discount(orm_discount)

        assert isinstance(domain_discount, Discount)

        assert domain_discount.home_operators == tuple(to_pk_list(orm_discount.home_operators.all()))
        assert domain_discount.partner_operators == tuple(to_pk_list(orm_discount.partner_operators.all()))

        assert domain_discount.direction == orm_discount.direction
        assert domain_discount.service_types == tuple(orm_discount.service_types)

        assert domain_discount.period == DatePeriod(orm_discount.start_date, orm_discount.end_date)

        assert domain_discount.currency_code == orm_discount.currency_code

        assert domain_discount.tax_type == orm_discount.tax_type
        assert domain_discount.volume_type == orm_discount.volume_type

        assert domain_discount.settlement_method == orm_discount.settlement_method

        assert domain_discount.call_destinations == tuple(orm_discount.call_destinations)
        assert domain_discount.called_countries == tuple(to_pk_list(orm_discount.called_countries.all()))
        assert domain_discount.traffic_segments == tuple(to_pk_list(orm_discount.traffic_segments.all()))

        assert domain_discount.qualifying_rule.direction == orm_discount.qualifying_direction
        assert domain_discount.qualifying_rule.service_types == orm_discount.qualifying_service_types
        assert domain_discount.qualifying_rule.basis == orm_discount.qualifying_basis
        assert domain_discount.qualifying_rule.lower_bound == orm_discount.qualifying_lower_bound
        assert domain_discount.qualifying_rule.upper_bound == orm_discount.qualifying_upper_bound

        assert domain_discount.parent_id is None

        assert domain_discount.imsi_count_type == IMSICountTypeEnum.NO_DATA

        assert domain_discount.model_type == DiscountModelTypeEnum.SINGLE_RATE_EFFECTIVE

        assert domain_discount.inbound_market_share == orm_discount.inbound_market_share

        assert domain_discount.include_premium == orm_agreement.include_premium
        assert domain_discount.include_premium_in_commitment == orm_agreement.include_premium_in_commitment

        assert domain_discount.commitment_distribution_parameters is not None

    def test_map_commitment_distribution_parameters(self):
        parameter = CommitmentDistributionParameter(
            home_operators=(55,),
            partner_operators=(345,),
            charge=Decimal("100.0"),
        )

        orm_discount = DiscountORMFactory(
            commitment_distribution_parameters=CommitmentDistributionParameterJSONEncoder.encode_many([parameter]),
        )

        domain_discount = from_orm_to_domain_discount(orm_discount)

        assert isinstance(domain_discount.commitment_distribution_parameters, tuple) is True

        param = domain_discount.commitment_distribution_parameters[0]

        assert isinstance(param, CommitmentDistributionParameter) is True

        assert param.home_operators == (55,)
        assert param.partner_operators == (345,)
        assert param.charge == Decimal("100")

    def test_nullable_fields(self):
        orm_discount = DiscountORMFactory(
            call_destinations=None,
            called_countries=None,
            qualifying_direction=None,
            qualifying_service_types=None,
            qualifying_basis=None,
            qualifying_lower_bound=None,
            qualifying_upper_bound=None,
            traffic_segments=None,
            imsi_count_type=None,
            model_type=None,
            inbound_market_share=None,
        )

        domain_discount = from_orm_to_domain_discount(orm_discount)

        assert domain_discount.call_destinations is None
        assert domain_discount.called_countries is None
        assert domain_discount.traffic_segments is None

        assert domain_discount.qualifying_rule is None

        assert domain_discount.imsi_count_type is None

        assert domain_discount.model_type is None
        assert domain_discount.inbound_market_share is None

    def test_parameters(self):
        orm_parameter = DiscountParameterORMFactory()

        domain_discount = from_orm_to_domain_discount(orm_parameter.discount)

        assert len(domain_discount.parameters) == 1

    def test_when_parameters_are_passed(self):
        orm_discount = DiscountORMFactory()

        p_factory = partial(
            DiscountParameterORMFactory,
            discount=orm_discount,
            calculation_type=DiscountCalculationTypeEnum.STEPPED_TIERED,
            balancing=None,
        )

        p1 = p_factory(lower_bound=Decimal("1000"))
        p2 = p_factory(lower_bound=Decimal("10"))
        p3 = p_factory(lower_bound=None)

        domain_discount = from_orm_to_domain_discount(
            orm_discount,
            domain_parameters=tuple([p1, p2, p3]),
        )

        assert len(domain_discount.parameters) == 3
        assert [p.lower_bound for p in domain_discount.parameters] == [Decimal("10"), Decimal("1000"), None]

    def test_parameters_are_sorted_by_lower_bound(self):
        orm_discount = DiscountORMFactory()

        p_factory = partial(
            DiscountParameterORMFactory,
            discount=orm_discount,
            calculation_type=DiscountCalculationTypeEnum.STEPPED_TIERED,
            balancing=None,
        )

        orm_p1 = p_factory(lower_bound=Decimal("100"))
        orm_p2 = p_factory(lower_bound=Decimal("10"))
        orm_p3 = p_factory(lower_bound=Decimal("1000"))

        domain_discount = from_orm_to_domain_discount(orm_discount)

        actual_lower_bounds = [p.lower_bound for p in domain_discount.parameters]
        expected_lower_bounds = [orm_p2.lower_bound, orm_p1.lower_bound, orm_p3.lower_bound]

        assert actual_lower_bounds == expected_lower_bounds

    def test_parameters_are_sorted_by_lower_bound_when_it_is_null(self):
        orm_discount = DiscountORMFactory()

        p_factory = partial(
            DiscountParameterORMFactory,
            discount=orm_discount,
            calculation_type=DiscountCalculationTypeEnum.STEPPED_TIERED,
            balancing=None,
        )

        orm_p1 = p_factory(lower_bound=None)
        orm_p2 = p_factory(lower_bound=Decimal("10"))

        domain_discount = from_orm_to_domain_discount(orm_discount)

        actual_lower_bounds = [p.lower_bound for p in domain_discount.parameters]
        expected_lower_bounds = [orm_p2.lower_bound, orm_p1.lower_bound]

        assert actual_lower_bounds == expected_lower_bounds

    def test_parameters_are_sorted_by_order_relevancy(self):
        orm_discount = DiscountORMFactory()

        p_factory = partial(
            DiscountParameterORMFactory,
            discount=orm_discount,
            balancing=None,
        )

        orm_p1 = p_factory(
            basis_value=Decimal("1.0000000000"),
            calculation_type=DiscountCalculationTypeEnum.STEPPED_TIERED,
            lower_bound=Decimal("0"),
        )
        orm_p2 = p_factory(
            basis_value=Decimal("3.0000000000"),
            calculation_type=DiscountCalculationTypeEnum.SEND_OR_PAY_TRAFFIC,
            lower_bound=Decimal("1000"),
        )
        orm_p3 = p_factory(
            basis_value=Decimal("2.0000000000"),
            calculation_type=DiscountCalculationTypeEnum.STEPPED_TIERED,
            lower_bound=Decimal("1000"),
        )

        domain_discount = from_orm_to_domain_discount(orm_discount)

        actual_basis_value = [p.basis_value for p in domain_discount.parameters]
        expected_basis_value = [orm_p2.basis_value, orm_p1.basis_value, orm_p3.basis_value]

        assert actual_basis_value == expected_basis_value

    def test_with_sub_discounts(self):
        orm_discount = DiscountORMFactory()
        orm_sub_discount = DiscountORMFactory(parent=orm_discount)

        domain_discount = from_orm_to_domain_discount(orm_discount)

        assert len(domain_discount.sub_discounts) == 1

        domain_sub_discount = domain_discount.sub_discounts[0]

        assert isinstance(domain_sub_discount, Discount)
        assert domain_sub_discount.id == orm_sub_discount.pk

    def test_when_discount_is_sub_discount(self):
        orm_sub_discount = DiscountORMFactory(parent=DiscountORMFactory())

        domain_discount = from_orm_to_domain_discount(orm_sub_discount)

        assert domain_discount.parent_id == orm_sub_discount.parent_id


@pytest.mark.django_db
class TestFromORMToDomainDiscountParameter:
    def test_with_nullable_values(self):
        orm_parameter = DiscountParameterORMFactory(
            basis=None,
            basis_value=None,
            balancing=None,
            bound_type=None,
            lower_bound=None,
            upper_bound=None,
            toll_rate=None,
            airtime_rate=None,
            fair_usage_rate=None,
            fair_usage_threshold=None,
            access_fee_rate=None,
            incremental_rate=None,
        )

        domain_parameter = from_orm_to_domain_discount_parameter(orm_parameter)

        assert domain_parameter.discount_id == orm_parameter.discount_id
        assert domain_parameter.calculation_type == orm_parameter.calculation_type

        assert domain_parameter.basis is None
        assert domain_parameter.basis_value is None

        assert domain_parameter.balancing is None

        assert domain_parameter.bound_type is None
        assert domain_parameter.lower_bound is None
        assert domain_parameter.upper_bound is None

        assert domain_parameter.toll_rate is None
        assert domain_parameter.airtime_rate is None

        assert domain_parameter.fair_usage_rate is None
        assert domain_parameter.fair_usage_threshold is None

        assert domain_parameter.access_fee_rate is None
        assert domain_parameter.incremental_rate is None
