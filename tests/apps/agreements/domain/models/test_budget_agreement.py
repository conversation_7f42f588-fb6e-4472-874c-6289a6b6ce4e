import pytest

from nga.apps.agreements.domain.events import BudgetAgreementConfirmedEvent
from nga.apps.agreements.domain.exceptions import InvalidAgreementStatusTransition
from nga.apps.agreements.enums import AgreementCalculationStatusEnum, AgreementStatusEnum
from nga.utils.dt import get_current_datetime_utc
from tests.factories.agreements import BudgetAgreementFactory


@pytest.mark.parametrize(
    "is_active,applied_at_empty",
    [
        (True, False),  # active but was not applied
        (True, True),  # active and applied
        (False, False),  # not active and not applied
        (False, True),  # not active but applied
    ],
)
def test_deactivate(is_active: bool, applied_at_empty: bool):
    applied_at = None
    if not applied_at_empty:
        applied_at = get_current_datetime_utc()

    budget_agreement = BudgetAgreementFactory(is_active=is_active, applied_at=applied_at)

    budget_agreement.deactivate()

    assert budget_agreement.is_active is False
    assert budget_agreement.applied_at is None
    assert budget_agreement.calculation_status == AgreementCalculationStatusEnum.NOT_APPLIED


@pytest.mark.parametrize(
    "is_active",
    [True, False],
)
def test_activate(is_active: bool):
    budget_agreement = BudgetAgreementFactory(is_active=is_active)

    budget_agreement.activate()

    assert budget_agreement.is_active is True


class TestSetStatus:
    @pytest.mark.parametrize(
        "status,next_statuses",
        [
            (
                AgreementStatusEnum.DRAFT,
                (AgreementStatusEnum.IN_REVIEW, AgreementStatusEnum.REJECTED, AgreementStatusEnum.BUDGETING),
            ),
            (
                AgreementStatusEnum.IN_REVIEW,
                (AgreementStatusEnum.DRAFT, AgreementStatusEnum.APPROVED, AgreementStatusEnum.REJECTED),
            ),
            (AgreementStatusEnum.REJECTED, (AgreementStatusEnum.DRAFT,)),
            (AgreementStatusEnum.APPROVED, (AgreementStatusEnum.LIVE, AgreementStatusEnum.CLOSED)),
            (AgreementStatusEnum.SUBMIT_FAILED, (AgreementStatusEnum.DRAFT, AgreementStatusEnum.APPROVED)),
            (AgreementStatusEnum.LIVE, (AgreementStatusEnum.CLOSED,)),
            (AgreementStatusEnum.BUDGETING, (AgreementStatusEnum.REJECTED,)),
            (AgreementStatusEnum.CLOSED, ()),
            (AgreementStatusEnum.AUTO_RENEWED, (AgreementStatusEnum.APPROVED, AgreementStatusEnum.REJECTED)),
        ],
    )
    def test_get_next_statuses(self, status, next_statuses):
        budget_agreement = BudgetAgreementFactory(status=status)

        assert budget_agreement.get_next_statuses() == next_statuses

    @pytest.mark.parametrize(
        "status,next_status",
        (
            # draft
            (AgreementStatusEnum.DRAFT, AgreementStatusEnum.DRAFT),
            (AgreementStatusEnum.DRAFT, AgreementStatusEnum.IN_REVIEW),
            (AgreementStatusEnum.DRAFT, AgreementStatusEnum.REJECTED),
            (AgreementStatusEnum.DRAFT, AgreementStatusEnum.BUDGETING),
            # in review
            (AgreementStatusEnum.IN_REVIEW, AgreementStatusEnum.DRAFT),
            (AgreementStatusEnum.IN_REVIEW, AgreementStatusEnum.APPROVED),
            (AgreementStatusEnum.IN_REVIEW, AgreementStatusEnum.REJECTED),
            (AgreementStatusEnum.IN_REVIEW, AgreementStatusEnum.IN_REVIEW),
            # rejected
            (AgreementStatusEnum.REJECTED, AgreementStatusEnum.DRAFT),
            (AgreementStatusEnum.REJECTED, AgreementStatusEnum.REJECTED),
            # live
            (AgreementStatusEnum.LIVE, AgreementStatusEnum.LIVE),
            (AgreementStatusEnum.LIVE, AgreementStatusEnum.CLOSED),
            # approved
            (AgreementStatusEnum.APPROVED, AgreementStatusEnum.APPROVED),
            (AgreementStatusEnum.APPROVED, AgreementStatusEnum.LIVE),
            (AgreementStatusEnum.APPROVED, AgreementStatusEnum.CLOSED),
            # submit failed
            (AgreementStatusEnum.SUBMIT_FAILED, AgreementStatusEnum.DRAFT),
            (AgreementStatusEnum.SUBMIT_FAILED, AgreementStatusEnum.APPROVED),
            # budgeting
            (AgreementStatusEnum.BUDGETING, AgreementStatusEnum.REJECTED),
            # closed
            (AgreementStatusEnum.CLOSED, AgreementStatusEnum.CLOSED),
            # auto_renewed
            (AgreementStatusEnum.AUTO_RENEWED, AgreementStatusEnum.APPROVED),
            (AgreementStatusEnum.AUTO_RENEWED, AgreementStatusEnum.REJECTED),
        ),
    )
    def test_set_valid_status(self, status, next_status):
        budget_agreement = BudgetAgreementFactory(status=status)

        budget_agreement.set_status(next_status)

        assert budget_agreement.status == next_status

    @pytest.mark.parametrize(
        "status,next_status",
        (
            # draft
            (AgreementStatusEnum.DRAFT, AgreementStatusEnum.LIVE),
            (AgreementStatusEnum.DRAFT, AgreementStatusEnum.CLOSED),
            (AgreementStatusEnum.DRAFT, AgreementStatusEnum.APPROVED),
            # in review
            (AgreementStatusEnum.IN_REVIEW, AgreementStatusEnum.CLOSED),
            (AgreementStatusEnum.IN_REVIEW, AgreementStatusEnum.LIVE),
            (AgreementStatusEnum.IN_REVIEW, AgreementStatusEnum.BUDGETING),
            # approved
            (AgreementStatusEnum.APPROVED, AgreementStatusEnum.DRAFT),
            (AgreementStatusEnum.APPROVED, AgreementStatusEnum.REJECTED),
            (AgreementStatusEnum.APPROVED, AgreementStatusEnum.IN_REVIEW),
            (AgreementStatusEnum.APPROVED, AgreementStatusEnum.BUDGETING),
            # submit failed
            (AgreementStatusEnum.SUBMIT_FAILED, AgreementStatusEnum.LIVE),
            (AgreementStatusEnum.SUBMIT_FAILED, AgreementStatusEnum.CLOSED),
            (AgreementStatusEnum.SUBMIT_FAILED, AgreementStatusEnum.IN_REVIEW),
            (AgreementStatusEnum.SUBMIT_FAILED, AgreementStatusEnum.REJECTED),
            (AgreementStatusEnum.SUBMIT_FAILED, AgreementStatusEnum.SUBMITTED),
            (AgreementStatusEnum.SUBMIT_FAILED, AgreementStatusEnum.BUDGETING),
            # live
            (AgreementStatusEnum.LIVE, AgreementStatusEnum.DRAFT),
            (AgreementStatusEnum.LIVE, AgreementStatusEnum.APPROVED),
            (AgreementStatusEnum.LIVE, AgreementStatusEnum.REJECTED),
            (AgreementStatusEnum.LIVE, AgreementStatusEnum.IN_REVIEW),
            (AgreementStatusEnum.LIVE, AgreementStatusEnum.BUDGETING),
            # closed
            (AgreementStatusEnum.CLOSED, AgreementStatusEnum.DRAFT),
            (AgreementStatusEnum.CLOSED, AgreementStatusEnum.APPROVED),
            (AgreementStatusEnum.CLOSED, AgreementStatusEnum.LIVE),
            (AgreementStatusEnum.CLOSED, AgreementStatusEnum.REJECTED),
            (AgreementStatusEnum.CLOSED, AgreementStatusEnum.IN_REVIEW),
            (AgreementStatusEnum.CLOSED, AgreementStatusEnum.BUDGETING),
            # rejected
            (AgreementStatusEnum.REJECTED, AgreementStatusEnum.IN_REVIEW),
            (AgreementStatusEnum.REJECTED, AgreementStatusEnum.APPROVED),
            (AgreementStatusEnum.REJECTED, AgreementStatusEnum.LIVE),
            (AgreementStatusEnum.REJECTED, AgreementStatusEnum.CLOSED),
            (AgreementStatusEnum.REJECTED, AgreementStatusEnum.BUDGETING),
            # budgeting
            (AgreementStatusEnum.BUDGETING, AgreementStatusEnum.DRAFT),
            (AgreementStatusEnum.BUDGETING, AgreementStatusEnum.IN_REVIEW),
            (AgreementStatusEnum.BUDGETING, AgreementStatusEnum.APPROVED),
            (AgreementStatusEnum.BUDGETING, AgreementStatusEnum.SUBMITTED),
            (AgreementStatusEnum.BUDGETING, AgreementStatusEnum.SUBMIT_FAILED),
            (AgreementStatusEnum.BUDGETING, AgreementStatusEnum.LIVE),
            (AgreementStatusEnum.BUDGETING, AgreementStatusEnum.CLOSED),
            # auto_renewed
            (AgreementStatusEnum.AUTO_RENEWED, AgreementStatusEnum.DRAFT),
            (AgreementStatusEnum.AUTO_RENEWED, AgreementStatusEnum.IN_REVIEW),
            (AgreementStatusEnum.AUTO_RENEWED, AgreementStatusEnum.SUBMITTED),
            (AgreementStatusEnum.AUTO_RENEWED, AgreementStatusEnum.SUBMIT_FAILED),
            (AgreementStatusEnum.AUTO_RENEWED, AgreementStatusEnum.LIVE),
            (AgreementStatusEnum.AUTO_RENEWED, AgreementStatusEnum.CLOSED),
            (AgreementStatusEnum.AUTO_RENEWED, AgreementStatusEnum.BUDGETING),
        ),
    )
    def test_invalid_status(self, status, next_status):
        budget_agreement = BudgetAgreementFactory(status=status)

        with pytest.raises(InvalidAgreementStatusTransition) as exc_info:
            budget_agreement.set_status(next_status)

        for allowed_next_status in budget_agreement.get_next_statuses():
            assert allowed_next_status.name in exc_info.value.message


def test_budget_agreement_approved_event_added():
    budget_agreement = BudgetAgreementFactory(status=AgreementStatusEnum.IN_REVIEW)

    budget_agreement.set_status(AgreementStatusEnum.APPROVED)

    event = next(e for e in budget_agreement.pop_events() if isinstance(e, BudgetAgreementConfirmedEvent))

    assert event.budget_agreement == budget_agreement


@pytest.mark.parametrize(
    "status,result",
    [
        (AgreementStatusEnum.DRAFT, False),
        (AgreementStatusEnum.IN_REVIEW, False),
        (AgreementStatusEnum.REJECTED, False),
        (AgreementStatusEnum.APPROVED, True),
        (AgreementStatusEnum.LIVE, True),
        (AgreementStatusEnum.CLOSED, True),
        (AgreementStatusEnum.SUBMIT_FAILED, False),
        (AgreementStatusEnum.SUBMITTED, True),
        (AgreementStatusEnum.BUDGETING, True),
        (AgreementStatusEnum.AUTO_RENEWED, True),
    ],
)
def test_is_confirmed(status: AgreementStatusEnum, result: bool):
    budget_agreement = BudgetAgreementFactory(status=status)

    assert budget_agreement.is_confirmed == result


@pytest.mark.parametrize(
    "initial_status,expected_status",
    [
        (AgreementCalculationStatusEnum.NOT_APPLIED, AgreementCalculationStatusEnum.NOT_APPLIED),
        (AgreementCalculationStatusEnum.APPLIED, AgreementCalculationStatusEnum.OUTDATED),
        (AgreementCalculationStatusEnum.FAILED, AgreementCalculationStatusEnum.NOT_APPLIED),
        (AgreementCalculationStatusEnum.OUTDATED, AgreementCalculationStatusEnum.OUTDATED),
    ],
)
def test_reset_calculation_status_after_modification(
    initial_status: AgreementCalculationStatusEnum,
    expected_status: AgreementCalculationStatusEnum,
):
    budget_agreement = BudgetAgreementFactory(calculation_status=initial_status)

    budget_agreement.reset_calculation_status_after_modification()

    assert budget_agreement.calculation_status == expected_status


def test_hash():
    budget_agreement = BudgetAgreementFactory()

    assert hash(budget_agreement) == budget_agreement.id
