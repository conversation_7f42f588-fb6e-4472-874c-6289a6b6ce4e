from copy import copy
from datetime import date

import pytest
from mediatr import Mediator

from nga.apps.agreements.commands import BulkRenewBudgetAgreementCommand
from nga.apps.agreements.domain.exceptions import (
    RenewedAgreementPeriodOverrideBudgetPeriodError,
)
from nga.apps.agreements.domain.services import BudgetAgreementRenewService
from nga.apps.agreements.dto import BudgetAgreementError
from nga.apps.agreements.enums import AgreementStatusEnum
from nga.apps.budgets.commands import UpdateBudgetAfterAgreementsModifiedCommand
from nga.core.types import DatePeriod, Month
from nga.utils.collections import to_id_list
from tests.apps.agreements.fakes import InMemoryBudgetAgreementRepository, InMemoryDiscountRepository
from tests.apps.budgets.fakes import InMemoryBudgetRepository
from tests.factories.agreements import BudgetAgreementFactory
from tests.factories.agreements.domain import (
    AgreementNegotiatorFactory,
    CommitmentDistributionParameterFactory,
    DiscountFactory,
    DiscountParameterFactory,
)
from tests.factories.budgets import BudgetFactory


class TestRenewBudgetAgreementCommandHandler:
    def test_agreement_renewed_successfully(self, override_deps, in_memory_mediator, in_memory_uow):
        negotiator = AgreementNegotiatorFactory()

        budget = BudgetFactory(
            period__start_date=date(2023, 1, 1),
            period__end_date=date(2023, 6, 1),
        )

        budget_agreement = BudgetAgreementFactory(
            period__start_date=date(2023, 1, 1),
            period__end_date=date(2023, 3, 1),
            negotiator_id=negotiator.id,
            budget_id=budget.id,
        )

        discount_repository = InMemoryDiscountRepository(discounts=[])

        bulk_renew_cmd = BulkRenewBudgetAgreementCommand(
            budget_id=budget.id,
            budget_agreement_ids=[budget_agreement.id],
        )

        with override_deps(
            uow=in_memory_uow,
            mediator=in_memory_mediator,
            budget_repository=InMemoryBudgetRepository(budgets=[budget]),
            budget_agreement_repository=InMemoryBudgetAgreementRepository(agreements=[budget_agreement]),
            discount_repository=discount_repository,
        ):
            renewed_budget_agreements, errors = Mediator().send(bulk_renew_cmd)

        assert len(renewed_budget_agreements) == 1

        renewed_budget_agreement = renewed_budget_agreements[0]

        assert renewed_budget_agreement.name == f"{budget_agreement.name}_RENEWED"
        assert renewed_budget_agreement.home_operators == budget_agreement.home_operators
        assert renewed_budget_agreement.partner_operators == budget_agreement.partner_operators
        assert renewed_budget_agreement.status == AgreementStatusEnum.DRAFT
        assert renewed_budget_agreement.period.start_date == date(2023, 4, 1)
        assert renewed_budget_agreement.period.end_date == date(2023, 6, 30)
        assert renewed_budget_agreement.negotiator_id is None

        discounts = discount_repository.get_many(budget_agreement.agreement_id)
        renewed_discounts = discount_repository.get_many(renewed_budget_agreement.agreement_id)

        assert len(renewed_discounts) == len(discounts)

    @pytest.mark.parametrize(
        "start,end,expected_start,expected_end",
        [
            ((2023, 1), (2023, 12), (2024, 1), (2024, 12)),
            ((2023, 1), (2023, 6), (2023, 7), (2023, 12)),
            ((2023, 1), (2023, 3), (2023, 4), (2023, 6)),
            ((2023, 9), (2023, 11), (2023, 12), (2024, 2)),
            ((2023, 10), (2023, 12), (2024, 1), (2024, 3)),
        ],
    )
    def test_agreement_renewed_period_calculation(
        self,
        # test parameters
        start: tuple[int, int],
        end: tuple[int, int],
        expected_start: tuple[int, int],
        expected_end: tuple[int, int],
        # fixtures
        override_deps,
        in_memory_mediator,
        in_memory_uow,
    ):
        budget = BudgetFactory(
            period__start_date=date(2023, 1, 1),
            period__end_date=date(2024, 12, 1),
        )

        budget_agreement = BudgetAgreementFactory(
            period=DatePeriod(date(start[0], start[1], 1), date(end[0], end[1], 1)),
            budget_id=budget.id,
        )

        bulk_renew_cmd = BulkRenewBudgetAgreementCommand(
            budget_id=budget.id,
            budget_agreement_ids=[budget_agreement.id],
        )

        with override_deps(
            uow=in_memory_uow,
            mediator=in_memory_mediator,
            budget_repository=InMemoryBudgetRepository(budgets=[budget]),
            budget_agreement_repository=InMemoryBudgetAgreementRepository(agreements=[budget_agreement]),
            discount_repository=InMemoryDiscountRepository(discounts=[]),
        ):
            renewed_budget_agreements, errors = Mediator().send(bulk_renew_cmd)

        assert len(renewed_budget_agreements) == 1

        renewed_budget_agreement = renewed_budget_agreements[0]

        assert renewed_budget_agreement.period == DatePeriod(
            Month.create_from_year_month(*expected_start),
            Month.create_from_year_month(*expected_end),
        )

    def test_renewed_agreement_is_persisted(self, override_deps, in_memory_mediator, in_memory_uow):
        budget = BudgetFactory(
            period__start_date=date(2023, 1, 1),
            period__end_date=date(2023, 12, 1),
        )
        agreement = BudgetAgreementFactory(
            budget_id=budget.id,
            period__start_date=date(2023, 1, 1),
            period__end_date=date(2023, 6, 1),
        )
        budget_agreement_repository = InMemoryBudgetAgreementRepository(agreements=[agreement])

        bulk_renew_cmd = BulkRenewBudgetAgreementCommand(
            budget_id=budget.id,
            budget_agreement_ids=[agreement.id],
        )

        with override_deps(
            uow=in_memory_uow,
            mediator=in_memory_mediator,
            budget_repository=InMemoryBudgetRepository(budgets=[budget]),
            budget_agreement_repository=budget_agreement_repository,
            discount_repository=InMemoryDiscountRepository(discounts=[]),
        ):
            renewed_budget_agreements, errors = Mediator().send(bulk_renew_cmd)

        assert len(renewed_budget_agreements) == 1

        renewed_budget_agreement = renewed_budget_agreements[0]

        assert len(budget_agreement_repository.agreements) == 2
        assert renewed_budget_agreement in budget_agreement_repository.agreements

        assert in_memory_uow.committed is True

    def test_agreement_discounts_are_renewed(self, override_deps, in_memory_mediator, in_memory_uow):
        budget = BudgetFactory(
            period__start_date=date(2024, 1, 1),
            period__end_date=date(2024, 8, 1),
        )
        budget_agreement = BudgetAgreementFactory(
            budget_id=budget.id,
            period__start_date=date(2024, 1, 1),
            period__end_date=date(2024, 4, 1),
        )
        budget_agreement_repository = InMemoryBudgetAgreementRepository(agreements=[budget_agreement])

        discount = DiscountFactory(
            agreement_id=budget_agreement.agreement_id,
            period=DatePeriod(date(2023, 1, 1), date(2023, 2, 1)),
            parameters=[DiscountParameterFactory()],
            period__start_date=date(2024, 2, 1),
            period__end_date=date(2024, 4, 1),
            commitment_distribution_parameters=CommitmentDistributionParameterFactory(),
        )
        initial_discount_period = copy(discount.period)
        sub_discount = DiscountFactory(parent_id=discount.id)
        discount.add_sub_discount(sub_discount)

        discount_repository = InMemoryDiscountRepository(discounts=[discount])

        bulk_renew_cmd = BulkRenewBudgetAgreementCommand(
            budget_id=budget.id,
            budget_agreement_ids=[budget_agreement.id],
        )

        with override_deps(
            uow=in_memory_uow,
            mediator=in_memory_mediator,
            budget_repository=InMemoryBudgetRepository(budgets=[budget]),
            budget_agreement_repository=budget_agreement_repository,
            discount_repository=discount_repository,
        ):
            renewed_budget_agreements, errors = Mediator().send(bulk_renew_cmd)

        assert len(renewed_budget_agreements) == 1

        renewed_budget_agreement = renewed_budget_agreements[0]

        renewed_discounts = discount_repository.get_many(renewed_budget_agreement.agreement_id)

        assert len(renewed_discounts) == 1
        renewed_discount = renewed_discounts[0]

        assert renewed_discount.id != discount.id
        assert renewed_discount.commitment_distribution_parameters == discount.commitment_distribution_parameters

        assert len(renewed_discount.parameters) == 1
        param = discount.parameters[0]
        renewed_param = renewed_discount.parameters[0]
        assert renewed_param.discount_id != param.discount_id

        assert len(renewed_discount.sub_discounts) == len(discount.sub_discounts)
        assert renewed_discount.sub_discounts[0].id != discount.sub_discounts[0].id

        assert renewed_discount.period == BudgetAgreementRenewService.renew_discount_period(
            initial_discount_period, budget_agreement.period
        )

    def test_sub_discounts_are_renewed(self, override_deps, in_memory_mediator, in_memory_uow):
        budget = BudgetFactory(
            period__start_date=date(2024, 1, 1),
            period__end_date=date(2024, 8, 1),
        )
        budget_agreement = BudgetAgreementFactory(
            budget_id=budget.id,
            period__start_date=date(2024, 1, 1),
            period__end_date=date(2024, 4, 1),
        )
        initial_budget_agreement_period = copy(budget_agreement.period)

        budget_agreement_repository = InMemoryBudgetAgreementRepository([budget_agreement])

        discount = DiscountFactory(agreement_id=budget_agreement.agreement_id)
        sub_discount = DiscountFactory(
            agreement_id=budget_agreement.agreement_id,
            parent_id=discount.id,
            period__start_date=date(2024, 2, 1),
            period__end_date=date(2024, 4, 1),
        )

        initial_sub_discount_period = copy(sub_discount.period)

        discount.add_sub_discount(sub_discount)

        discount_repository = InMemoryDiscountRepository([discount])

        bulk_renew_cmd = BulkRenewBudgetAgreementCommand(
            budget_id=budget.id,
            budget_agreement_ids=[budget_agreement.id],
        )

        with override_deps(
            uow=in_memory_uow,
            mediator=in_memory_mediator,
            budget_repository=InMemoryBudgetRepository(budgets=[budget]),
            budget_agreement_repository=budget_agreement_repository,
            discount_repository=discount_repository,
        ):
            renewed_budget_agreements, errors = Mediator().send(bulk_renew_cmd)

        assert len(renewed_budget_agreements) == 1

        renewed_budget_agreement = renewed_budget_agreements[0]

        renewed_discounts = discount_repository.get_many(renewed_budget_agreement.agreement_id)
        renewed_sub_discount = renewed_discounts[0].sub_discounts[0]

        expected_period = BudgetAgreementRenewService.renew_discount_period(
            initial_sub_discount_period, initial_budget_agreement_period
        )

        assert renewed_sub_discount.period == expected_period

    def test_when_renewed_agreement_period_overrode_budget_period(
        self, override_deps, in_memory_mediator, in_memory_uow
    ):
        budget = BudgetFactory(
            period__start_date=date(2025, 1, 1),
            period__end_date=date(2025, 6, 1),
        )
        budget_agreement = BudgetAgreementFactory(
            budget_id=budget.id,
            period=DatePeriod(date(2025, 1, 1), date(2025, 5, 1)),
        )

        bulk_renew_cmd = BulkRenewBudgetAgreementCommand(
            budget_id=budget.id,
            budget_agreement_ids=[budget_agreement.id],
        )

        with override_deps(
            uow=in_memory_uow,
            mediator=in_memory_mediator,
            budget_repository=InMemoryBudgetRepository(budgets=[budget]),
            budget_agreement_repository=InMemoryBudgetAgreementRepository(agreements=[budget_agreement]),
            discount_repository=InMemoryDiscountRepository(discounts=[]),
        ):
            renewed_budget_agreements, errors = Mediator().send(bulk_renew_cmd)

        assert len(renewed_budget_agreements) == 0

        assert len(errors) == 1

        expected_error = BudgetAgreementError(
            agreement_id=budget_agreement.id,
            detail=RenewedAgreementPeriodOverrideBudgetPeriodError().message,
        )

        assert expected_error in errors

    def test_when_agreement_is_not_intersected_with_other_agreements(
        self,
        override_deps,
        in_memory_mediator,
        in_memory_uow,
    ):
        budget = BudgetFactory(
            period__start_date=date(2023, 1, 1),
            period__end_date=date(2023, 4, 1),
        )
        budget_agreement = BudgetAgreementFactory(
            budget_id=budget.id,
            period__start_date=date(2023, 1, 1),
            period__end_date=date(2023, 2, 1),
        )

        existing_budget_agreement = copy(budget_agreement)
        existing_budget_agreement.id += 10
        existing_budget_agreement.is_active = True
        existing_budget_agreement.status = AgreementStatusEnum.DRAFT

        budget_agreement_repository = InMemoryBudgetAgreementRepository(
            agreements=[budget_agreement, existing_budget_agreement]
        )

        bulk_renew_cmd = BulkRenewBudgetAgreementCommand(
            budget_id=budget.id,
            budget_agreement_ids=[budget_agreement.id],
        )

        with override_deps(
            uow=in_memory_uow,
            mediator=in_memory_mediator,
            budget_repository=InMemoryBudgetRepository(budgets=[budget]),
            budget_agreement_repository=budget_agreement_repository,
            discount_repository=InMemoryDiscountRepository(discounts=[]),
        ):
            Mediator().send(bulk_renew_cmd)

    def test_update_budget_command_is_sent(self, override_deps, in_memory_mediator, in_memory_uow):
        budget = BudgetFactory(
            period__start_date=date(2023, 1, 1),
            period__end_date=date(2023, 4, 1),
        )
        budget_agreement = BudgetAgreementFactory(
            budget_id=budget.id,
            period__start_date=date(2023, 1, 1),
            period__end_date=date(2023, 2, 1),
        )

        bulk_renew_cmd = BulkRenewBudgetAgreementCommand(
            budget_id=budget.id,
            budget_agreement_ids=[budget_agreement.id],
        )

        with override_deps(
            uow=in_memory_uow,
            mediator=in_memory_mediator,
            budget_repository=InMemoryBudgetRepository(budgets=[budget]),
            budget_agreement_repository=InMemoryBudgetAgreementRepository(agreements=[budget_agreement]),
            discount_repository=InMemoryDiscountRepository(discounts=[]),
        ):
            Mediator().send(bulk_renew_cmd)

        update_budget_cmd = in_memory_mediator.get_message_by_type(UpdateBudgetAfterAgreementsModifiedCommand)
        assert update_budget_cmd.budget_id == budget_agreement.budget_id

    def test_renew_many(self, override_deps, in_memory_mediator, in_memory_uow):
        budget = BudgetFactory(
            period__start_date=date(2025, 1, 1),
            period__end_date=date(2025, 12, 1),
        )

        budget_agreement1 = BudgetAgreementFactory(
            budget_id=budget.id,
            period__start_date=date(2025, 1, 1),
            period__end_date=date(2025, 5, 1),
        )
        budget_agreement2 = BudgetAgreementFactory(
            budget_id=budget.id,
            period__start_date=date(2025, 6, 1),
            period__end_date=date(2025, 7, 1),
        )

        bulk_renew_cmd = BulkRenewBudgetAgreementCommand(
            budget_id=budget.id,
            budget_agreement_ids=[budget_agreement1.id, budget_agreement2.id],
        )

        with override_deps(
            uow=in_memory_uow,
            mediator=in_memory_mediator,
            budget_repository=InMemoryBudgetRepository(budgets=[budget]),
            budget_agreement_repository=InMemoryBudgetAgreementRepository(
                agreements=[budget_agreement1, budget_agreement2],
            ),
            discount_repository=InMemoryDiscountRepository(discounts=[]),
        ):
            renewed_budget_agreements, errors = Mediator().send(bulk_renew_cmd)

        assert len(renewed_budget_agreements) == 2
        assert len(errors) == 0

        assert in_memory_uow.total_commits == 2

    def test_renew_discounts_when_they_exist(self, override_deps, in_memory_mediator, in_memory_uow):
        budget_agreement = BudgetAgreementFactory()
        ba_discount = DiscountFactory(agreement_id=budget_agreement.agreement_id)

        renewed_budget_agreement = BudgetAgreementFactory()
        rba_discount = DiscountFactory(agreement_id=renewed_budget_agreement.agreement_id)

        discount_repository = InMemoryDiscountRepository(discounts=[ba_discount, rba_discount])

        BudgetAgreementRenewService.renew_discounts(budget_agreement, renewed_budget_agreement, discount_repository)

        renewed_discounts = discount_repository.get_many(renewed_budget_agreement.agreement_id)

        assert len(renewed_discounts) == 1
        assert renewed_discounts[0].id not in to_id_list([ba_discount, rba_discount])
