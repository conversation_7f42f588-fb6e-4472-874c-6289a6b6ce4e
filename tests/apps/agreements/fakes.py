from copy import copy, deepcopy
from datetime import date
from typing import Iterable, Optional, Sequence

from nga.apps.agreements.api.mappers import AbstractBudgetAgreementListSchemaMapper, AbstractDiscountSchemaMapper
from nga.apps.agreements.api.schemas import BudgetAgreementSchema, DiscountSchema
from nga.apps.agreements.domain.dto import BudgetAgreementCreateDTO, DiscountDTO, DiscountParameterDTO
from nga.apps.agreements.domain.exceptions import (
    AgreementDoesNotExist,
    AgreementNegotiatorDoesNotExist,
    BudgetAgreementDoesNotExist,
    DiscountDoesNotExist,
    DiscountValidationError,
)
from nga.apps.agreements.domain.models import (
    Agreement,
    AgreementNegotiator,
    BudgetAgreement,
    Discount,
    DiscountParameter,
)
from nga.apps.agreements.domain.repositories import (
    AbstractAgreementNegotiatorRepository,
    AbstractAgreementRepository,
    AbstractBudgetAgreementRepository,
    AbstractDiscountRepository,
)
from nga.apps.agreements.domain.specifications.abstract import AbstractDiscountSpecification
from nga.apps.agreements.enums import AgreementIntersectionTypeEnum, AgreementStatusEnum, DiscountDirectionEnum
from nga.apps.budgets.domain.dto import BudgetParametersFilters
from nga.apps.budgets.domain.models import BudgetTrafficRecord
from nga.apps.calculation.discounts.model_factory import AbstractDiscountModelFactory
from nga.apps.calculation.discounts.models.abstract import AbstractDiscountModel
from nga.apps.calculation.discounts.service import AbstractDiscountCalculationService
from nga.apps.references.domain.models import Country, Operator
from nga.core.enums import ServiceTypeEnum
from nga.core.types import DatePeriod
from tests.factories.agreements import BudgetAgreementFactory
from tests.factories.agreements.domain import DiscountFactory


class InMemoryBudgetAgreementRepository(AbstractBudgetAgreementRepository):
    def __init__(self, agreements: Optional[list[BudgetAgreement]] = None) -> None:
        self.agreements = agreements or []

    def get_many(
        self,
        *,
        budget_id: Optional[int] = None,
        budget_agreement_ids: Optional[list[int]] = None,
        budget_parameters: Optional[BudgetParametersFilters] = None,
        agreement_id: Optional[int] = None,
        start_date: Optional[date] = None,
        end_date: Optional[date] = None,
        statuses: Optional[AgreementStatusEnum] = None,
        search: Optional[str] = None,
        order: Optional[str] = None,
        only_active: Optional[bool] = None,
        only_modified: Optional[bool] = None,
    ) -> tuple[BudgetAgreement, ...]:

        agreements = list(self.agreements)

        if budget_id:
            agreements = [a for a in agreements if a.budget_id == budget_id]

        if budget_agreement_ids:
            agreements = [a for a in agreements if a.id in budget_agreement_ids]

        if budget_parameters:
            if budget_parameters.home_operators:
                agreements = [a for a in agreements if set(a.home_operators).issubset(budget_parameters.home_operators)]

        if agreement_id is not None:
            agreements = [a for a in agreements if a.agreement_id == agreement_id]

        if start_date:
            agreements = [a for a in agreements if a.period.start_date >= start_date]

        if end_date:
            agreements = [a for a in agreements if a.period.end_date <= end_date]

        if statuses:
            agreements = [a for a in agreements if a.status in statuses]

        if only_active is not None:
            agreements = [a for a in agreements if a.is_active is only_active]

        # TODO: cover budget parameters, period, search, order

        return tuple(agreements)

    def get_by_id(self, budget_agreement_id: int) -> BudgetAgreement:
        try:
            return deepcopy(next(a for a in self.agreements if a.id == budget_agreement_id))
        except StopIteration:
            raise BudgetAgreementDoesNotExist(budget_agreement_id)

    def get_by_external_id(self, external_id: int, budget_id: int) -> Optional[BudgetAgreement]:
        try:
            return deepcopy(
                next(a for a in self.agreements if a.external_id == external_id and a.budget_id == budget_id)
            )
        except StopIteration:
            return None

    def get_by_parent_id(self, parent_id: int, budget_id: int) -> Optional[BudgetAgreement]:
        try:
            return deepcopy(next(a for a in self.agreements if a.parent_id == parent_id and a.budget_id == budget_id))
        except StopIteration:
            return None

    def create(self, agreement_dto: BudgetAgreementCreateDTO, budget_id: int) -> BudgetAgreement:
        agreement = BudgetAgreementFactory(
            name=agreement_dto.name,
            home_operators=agreement_dto.home_operators,
            partner_operators=agreement_dto.partner_operators,
            period=agreement_dto.period,
            budget_id=budget_id,
            status=AgreementStatusEnum.DRAFT,
            negotiator_id=agreement_dto.negotiator_id,
            include_satellite=agreement_dto.include_satellite,
            include_premium=agreement_dto.include_premium,
            include_premium_in_commitment=agreement_dto.include_premium_in_commitment,
            is_rolling=agreement_dto.is_rolling,
        )

        self.agreements.append(agreement)

        return agreement

    def delete_by_id(self, budget_agreement_id: int, *, for_all_budgets: bool = False) -> None:
        budget_agreement = self.get_by_id(budget_agreement_id)

        self.agreements = [a for a in self.agreements if a.id != budget_agreement_id]

        # Custom implementation of CASCADE agreements removing
        nested_agreement = self.get_by_parent_id(
            parent_id=budget_agreement.agreement_id, budget_id=budget_agreement.budget_id
        )

        if nested_agreement is not None:
            self.delete_by_id(nested_agreement.id)

    def has_intersection(
        self,
        budget_agreement: BudgetAgreement,
        with_statuses: Optional[Sequence[AgreementStatusEnum]] = None,
        with_active: Optional[bool] = None,
    ) -> bool:
        # verification logic is rather simplified to tests

        agreements = [a for a in self.agreements if a != budget_agreement]

        agreements = [
            a
            for a in agreements
            if a.budget_id == budget_agreement.budget_id
            and a.home_operators == budget_agreement.home_operators
            and a.partner_operators == budget_agreement.partner_operators
            and a.period.has_intersection_with(budget_agreement.period)
        ]

        if with_statuses:
            agreements = [a for a in agreements if a.status in with_statuses]

        if with_active:
            agreements = [a for a in agreements if a.is_active == with_active]

        return len(agreements) > 0

    def get_intersected_many(
        self,
        budget_id: int,
        home_operators: list[int],
        partner_operators: list[int],
        period: DatePeriod,
        excluded_id: Optional[int] = None,
        with_statuses: Optional[Sequence[AgreementStatusEnum]] = None,
        with_active: Optional[bool] = None,
    ) -> tuple[BudgetAgreement, ...]:

        agreements = [
            a
            for a in self.agreements
            if a.budget_id == budget_id
            and a.home_operators == home_operators
            and a.partner_operators == partner_operators
            and set(a.period).intersection(set(period))
        ]

        if excluded_id is not None:
            agreements = [a for a in agreements if a.id != excluded_id]

        if with_statuses:
            agreements = [a for a in agreements if a.status in with_statuses]

        if with_active:
            agreements = [a for a in agreements if a.is_active == with_active]

        return tuple(agreements)

    def get_linked_parents(self, budget_agreement_id: int, budget_id: int) -> list[BudgetAgreement]:
        linked_parents = []

        budget_agreement = self.get_by_id(budget_agreement_id)

        while budget_agreement.parent_id is not None:
            budget_agreement = next(
                (
                    ba
                    for ba in self.agreements
                    if ba.agreement_id == budget_agreement.parent_id and ba.budget_id == budget_id
                )
            )

            linked_parents.append(budget_agreement)

        return linked_parents

    def get_linked_subsidiaries(self, budget_agreement_id: int, budget_id: int) -> list[BudgetAgreement]:
        linked_subsidiaries = []

        budget_agreement = self.get_by_id(budget_agreement_id)

        parent_agreement = self.get_by_parent_id(budget_agreement.agreement_id, budget_id)

        while parent_agreement is not None:
            linked_subsidiaries.append(parent_agreement)

            parent_agreement = self.get_by_parent_id(parent_agreement.agreement_id, budget_id)

        return linked_subsidiaries

    def copy(self, budget_agreement: BudgetAgreement, target_budget_id: int) -> BudgetAgreement:
        new_agreement = deepcopy(budget_agreement)
        new_agreement.id = BudgetAgreementFactory().id
        new_agreement.budget_id = target_budget_id

        if budget_agreement.is_confirmed is False:
            new_agreement.agreement_id = budget_agreement.agreement_id**2
            new_agreement.negotiator_id = None

        self.agreements.append(new_agreement)

        return new_agreement

    def count(
        self,
        budget_id: int,
        *,
        budget_parameters: Optional[BudgetParametersFilters] = None,
        is_active: Optional[bool] = None,
    ) -> int:
        # Note: Not all filters are supported

        agreements = [ba for ba in self.agreements if ba.budget_id == budget_id]

        if is_active is not None:
            agreements = [a for a in agreements if a.is_active == is_active]

        return len(agreements)

    def save(self, agreement: BudgetAgreement) -> BudgetAgreement:
        self.agreements = [a for a in self.agreements if a.id != agreement.id]

        self.agreements.append(agreement)

        return agreement

    def update_many(self, budget_agreements: Sequence[BudgetAgreement]) -> None:
        pass


class InMemoryBudgetAgreementListSchemaMapper(AbstractBudgetAgreementListSchemaMapper):
    def __init__(
        self,
        countries: Optional[list[Country]] = None,
        operators: Optional[list[Operator]] = None,
        agreement_negotiators: Optional[list[AgreementNegotiator]] = None,
    ):
        self.countries_map = {c.id: c for c in countries} if countries else {}
        self.operators_map = {o.id: o for o in operators} if operators else {}
        self.agreement_negotiators_map = {n.id: n for n in agreement_negotiators} if agreement_negotiators else {}

    def map(
        self,
        agreements: Iterable[BudgetAgreement],
        intersection_map: Optional[dict[int, list[AgreementIntersectionTypeEnum]]] = None,
    ) -> list[BudgetAgreementSchema]:

        if intersection_map is None:
            intersection_map = {a.id: [AgreementIntersectionTypeEnum.ONE_OF] for a in agreements}

        list_items = []

        for a in agreements:
            partner_operators = [self.operators_map[o_id] for o_id in a.partner_operators]
            partner_countries_ids = set(o.country_id for o in partner_operators)

            next_statuses = list(a.get_next_statuses()) if a.is_confirmed is False else []

            item = BudgetAgreementSchema(
                id=a.id,
                budget_id=a.budget_id,
                agreement_id=a.agreement_id,
                name=a.name,
                status=a.status,
                next_statuses=next_statuses,
                calculation_status=a.calculation_status,
                start_date=a.period.start_date,
                end_date=a.period.end_date,
                is_active=a.is_active,
                negotiator=self.agreement_negotiators_map[a.negotiator_id] if a.negotiator_id else None,
                home_operators=[self.operators_map[o_id] for o_id in a.home_operators],
                partner_operators=[self.operators_map[o_id] for o_id in a.partner_operators],
                partner_countries=[self.countries_map[c_id] for c_id in partner_countries_ids],
                intersection_types=intersection_map[a.id],
                include_satellite=a.include_satellite,
                include_premium=a.include_premium,
                include_premium_in_commitment=a.include_premium_in_commitment,
                is_rolling=a.is_rolling,
                updated_at=a.updated_at,
                applied_at=a.applied_at,
            )
            list_items.append(item)

        return list_items


class InMemoryAgreementRepository(AbstractAgreementRepository):
    def __init__(self, agreements: Optional[list[Agreement]] = None) -> None:
        self._agreements = agreements or []

    def get_by_id(self, agreement_id: int) -> Agreement:
        try:
            return next(a for a in self._agreements if a.id == agreement_id)
        except StopIteration:
            raise AgreementDoesNotExist(agreement_id)


class InMemoryDiscountRepository(AbstractDiscountRepository):
    def __init__(self, discounts: Optional[list[Discount]] = None) -> None:
        self.discounts = discounts or []

    def get_by_id(self, discount_id: int) -> Discount:
        try:
            return deepcopy(next(d for d in self.discounts if d.id == discount_id))
        except StopIteration:
            raise DiscountDoesNotExist(discount_id)

    def get_many(
        self,
        agreement_id: int,
        *,
        discount_ids: Optional[list[int]] = None,
        home_operators: Optional[list[int]] = None,
        partner_operators: Optional[list[int]] = None,
        directions: Optional[list[DiscountDirectionEnum]] = None,
        service_types: Optional[list[ServiceTypeEnum]] = None,
        period: Optional[DatePeriod] = None,
    ) -> tuple[Discount, ...]:
        discounts = tuple(d for d in self.discounts if d.agreement_id == agreement_id and d.parent_id is None)

        if discount_ids is not None:
            discounts = tuple(d for d in discounts if d.id in discount_ids)

        if home_operators is not None:
            discounts = tuple(d for d in discounts if set(d.home_operators).intersection(home_operators))

        if partner_operators is not None:
            discounts = tuple(d for d in discounts if set(d.partner_operators).intersection(partner_operators))

        if directions is not None:
            discounts = tuple(d for d in discounts if d.direction in directions)

        if service_types is not None:
            service_types_set = set(service_types)

            discounts = tuple(d for d in discounts if set(d.service_types).intersection(service_types_set))

        if period is not None:
            discounts = tuple(d for d in discounts if d.period in period)

        return discounts

    def delete_by_id(self, discount_id: int) -> None:
        self.discounts = [d for d in self.discounts if d.id != discount_id]

    def create(self, agreement_id: int, discount_dto: DiscountDTO) -> Discount:
        discount = DiscountFactory(
            agreement_id=agreement_id,
            home_operators=discount_dto.home_operators,
            partner_operators=discount_dto.partner_operators,
            direction=discount_dto.direction,
            period=DatePeriod(discount_dto.start_date, discount_dto.end_date),
            service_types=discount_dto.service_types,
            currency_code=discount_dto.currency_code,
            tax_type=discount_dto.tax_type,
            volume_type=discount_dto.volume_type,
            settlement_method=discount_dto.settlement_method,
            imsi_count_type=discount_dto.imsi_count_type,
            qualifying_rule=discount_dto.qualifying_rule,
            parameters=tuple(),
            model_type=discount_dto.model_type,
            above_commitment_rate=discount_dto.above_commitment_rate,
            commitment_distribution_parameters=discount_dto.commitment_distribution_parameters,
        )

        self.discounts.append(discount)

        self.set_parameters(discount.id, discount_dto.parameters)

        discount = self.get_by_id(discount.id)  # to get fresh version with set parameters

        return discount

    def create_sub_discount(self, discount_id: int, sub_discount_dto: DiscountDTO) -> Discount:
        discount = self.get_by_id(discount_id)

        sub_discount = self.create(discount.agreement_id, sub_discount_dto)

        discount.add_sub_discount(sub_discount)

        self.save(discount)

        self.delete_by_id(sub_discount.id)

        return sub_discount

    def save(self, discount: Discount) -> Discount:
        if not discount.is_parent:
            parent_discount = self.get_by_id(discount.parent_id)

            parent_discount.sub_discounts = (sb for sb in parent_discount.sub_discounts if sb.id != discount.id)
            parent_discount.add_sub_discount(discount)

            self.save(parent_discount)

        self.delete_by_id(discount.id)
        self.discounts.append(copy(discount))

        return discount

    def set_parameters(
        self,
        discount_id: int,
        parameters_dtos: tuple[DiscountParameterDTO, ...],
    ) -> tuple[DiscountParameter, ...]:
        discount = self.get_by_id(discount_id)

        parameters = tuple(
            DiscountParameter(
                discount_id=discount.id,
                calculation_type=p_dto.calculation_type,
                basis=p_dto.basis,
                basis_value=p_dto.basis_value,
                balancing=p_dto.balancing,
                bound_type=p_dto.bound_type,
                lower_bound=p_dto.lower_bound,
                upper_bound=p_dto.upper_bound,
                toll_rate=p_dto.toll_rate,
                airtime_rate=p_dto.airtime_rate,
                fair_usage_rate=p_dto.fair_usage_rate,
                fair_usage_threshold=p_dto.fair_usage_threshold,
                access_fee_rate=p_dto.access_fee_rate,
                incremental_rate=p_dto.incremental_rate,
            )
            for p_dto in parameters_dtos
        )

        discount.parameters = parameters

        self.save(discount)

        return parameters

    def has_intersection_within_agreement(self, discount: Discount) -> bool:
        return discount in self.discounts

    def has_intersection_between_sub_discounts(self, discount: Discount) -> bool:
        pass

    def delete_many(self, agreement_id: int) -> None:
        self.discounts = [d for d in self.discounts if d.agreement_id != agreement_id]


class FakeDiscountSpecification(AbstractDiscountSpecification):
    def __init__(self):
        self.verified = False

    def verify(self, discount: Discount) -> None:
        self.verified = True


class FakeErrorDiscountSpecification(AbstractDiscountSpecification):
    def verify(self, discount: Discount) -> None:
        raise DiscountValidationError("fake spec error")


class FakeDiscountSchemaMapper(AbstractDiscountSchemaMapper):
    def __init__(self, schemas: list[DiscountSchema]) -> None:
        self._schemas = schemas

    def map_one(self, discount: Discount) -> DiscountSchema:
        return self._schemas[0]

    def map_many(self, discounts: Iterable[Discount]) -> tuple[DiscountSchema, ...]:
        return tuple(self._schemas)


class FakeDiscountCalculationService(AbstractDiscountCalculationService):
    def __init__(self, discounted_traffic_records: list[BudgetTrafficRecord]) -> None:
        self._discounted_traffic_records = discounted_traffic_records

    def apply_discount_to_traffic(
        self,
        discount: Discount,
        budget_snapshot_id: int,
        traffic_records: Sequence[BudgetTrafficRecord],
    ) -> list[BudgetTrafficRecord]:
        return self._discounted_traffic_records or traffic_records


class FakeDiscountModelFactory(AbstractDiscountModelFactory):
    def __init__(self, discount_calculation_model: AbstractDiscountModel) -> None:
        self._discount_calculation_model = discount_calculation_model

    def create_from_discount(self, discount: Discount) -> AbstractDiscountModel:
        return self._discount_calculation_model


class FakeDiscountModel(AbstractDiscountModel):
    def __init__(self, discount_charge_records) -> None:
        self._discount_charge_records = discount_charge_records

    def apply_discount(
        self, discount: Discount, traffic_records: list[BudgetTrafficRecord]
    ) -> list[BudgetTrafficRecord]:
        return traffic_records


class FakeBudgetAgreementRepository(InMemoryBudgetAgreementRepository):
    def has_intersection(
        self,
        budget_agreement: BudgetAgreement,
        with_statuses: Optional[Sequence[AgreementStatusEnum]] = None,
        with_active: Optional[bool] = None,
    ) -> bool:
        return True


class InMemoryAgreementNegotiatorRepository(AbstractAgreementNegotiatorRepository):
    def __init__(self, negotiators: list[AgreementNegotiator]) -> None:
        self.negotiators = negotiators

    def get_many(self, negotiator_ids: Optional[list[int]] = None) -> list[AgreementNegotiator]:
        return self.negotiators

    def get_by_id(self, agreement_negotiator_id: int) -> AgreementNegotiator:
        try:
            return next(n for n in self.negotiators if n.id == agreement_negotiator_id)
        except StopIteration:
            raise AgreementNegotiatorDoesNotExist(agreement_negotiator_id)
