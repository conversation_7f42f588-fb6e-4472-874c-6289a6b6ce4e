from datetime import datetime
from unittest.mock import Mock, call

from nga.apps.budgets.domain.dto import BudgetComponentsState, BudgetParametersFilters
from nga.apps.budgets.enums import BudgetCalculationTypeEnum
from nga.apps.budgets.providers import BudgetComponentsProvider
from nga.core.types import Month
from tests.apps.budgets.fakes import InMemoryBudgetRepository, InMemoryForecastRuleProvider
from tests.factories.budgets import BudgetCalculationFactory, BudgetFactory


class TestBudgetComponentsProvider:
    def test_get_quantity_without_filters(
        self,
        forecast_rules_provider_mock: Mock,
        budget_agreement_provider_mock: Mock,
    ):
        _budget_id = 1

        total_forecast_rules = 10
        total_agreements = 8

        forecast_rules_provider_mock.get_total_rules.return_value = total_forecast_rules
        budget_agreement_provider_mock.get_total_agreements.return_value = total_agreements

        budget_items_provider = BudgetComponentsProvider(
            forecast_rule_provider=forecast_rules_provider_mock,
            budget_agreement_provider=budget_agreement_provider_mock,
            budget_repository=Mock(),
        )

        quantity = budget_items_provider.get_quantity(_budget_id)

        assert quantity.budget_id == _budget_id
        assert quantity.forecast_rules == total_forecast_rules
        assert quantity.agreements == total_agreements

        assert quantity.iot_rates == 0

        forecast_rules_provider_mock.get_total_rules.assert_called_once_with(_budget_id, None)

        budget_agreement_provider_mock.get_total_agreements.assert_has_calls(
            [
                call(_budget_id, budget_parameters=None),
                call(_budget_id, budget_parameters=None, is_active=True),
            ]
        )

    def test_get_quantity_with_filters(
        self,
        forecast_rules_provider_mock: Mock,
        budget_agreement_provider_mock: Mock,
    ):
        _budget_id = 1

        total_forecast_rules = 10
        total_agreements = 8

        forecast_rules_provider_mock.get_total_rules.return_value = total_forecast_rules
        budget_agreement_provider_mock.get_total_agreements.return_value = total_agreements

        budget_items_provider = BudgetComponentsProvider(
            forecast_rule_provider=forecast_rules_provider_mock,
            budget_agreement_provider=budget_agreement_provider_mock,
            budget_repository=Mock(),
        )

        filters = BudgetParametersFilters(
            home_operators=[1],
            partner_operators=[2, 3],
            partner_countries=[4],
        )

        quantity = budget_items_provider.get_quantity(_budget_id, filters)

        assert quantity.forecast_rules == total_forecast_rules
        assert quantity.agreements == total_agreements

        forecast_rules_provider_mock.get_total_rules.assert_called_once_with(_budget_id, filters)

        budget_agreement_provider_mock.get_total_agreements.assert_has_calls(
            [
                call(_budget_id, budget_parameters=filters),
                call(_budget_id, budget_parameters=filters, is_active=True),
            ]
        )


class TestGetBudgetComponentsState:
    def test_ok(self):
        budget = BudgetFactory()

        full_with_traffic_update_calculation = BudgetCalculationFactory(
            type=BudgetCalculationTypeEnum.FULL_WITH_TRAFFIC_UPDATE,
            budget_lhm=Month.create_from_year_month(2025, 6),
            traffic_lhm=Month.create_from_year_month(2025, 5),
            created_at=datetime(2025, 7, 1, 10, 1, 1),
        )
        full_calculation = BudgetCalculationFactory(
            type=BudgetCalculationTypeEnum.FULL_WITHOUT_TRAFFIC_UPDATE,
            distribution_lhm=Month.create_from_year_month(2025, 7),
            created_at=datetime(2025, 7, 1, 11, 1, 1),
        )

        budget_repository = InMemoryBudgetRepository(
            calculations=[full_calculation, full_with_traffic_update_calculation]
        )

        budget_components_provider = BudgetComponentsProvider(
            forecast_rule_provider=InMemoryForecastRuleProvider(),
            budget_repository=budget_repository,
            budget_agreement_provider=Mock(),
        )

        state = budget_components_provider.get_state(budget)

        assert isinstance(state, BudgetComponentsState)
        assert state.budget_id == budget.id

        assert state.forecast_rules_applied_at == full_with_traffic_update_calculation.forecast_rules_applied_at
        assert state.forecast_rules_last_modified_at == budget.forecast_rules_modified_at

        assert state.budget_traffic_synchronized_at == full_calculation.forecast_rules_applied_at
        assert state.historical_traffic_last_modified_at == budget.historical_traffic_modified_at

        assert state.agreements_last_modified_at == budget.agreements_last_modified_at

        assert state.budget_lhm == full_calculation.budget_lhm
        assert state.distribution_lhm == full_calculation.distribution_lhm
        assert state.traffic_lhm == full_with_traffic_update_calculation.traffic_lhm

    def test_when_full_calculation_with_traffic_update_does_not_exist(self):
        budget = BudgetFactory()

        full_calculation = BudgetCalculationFactory(type=BudgetCalculationTypeEnum.FULL_WITHOUT_TRAFFIC_UPDATE)
        budget_repository = InMemoryBudgetRepository(calculations=[full_calculation])

        forecast_rule_provider = InMemoryForecastRuleProvider()

        budget_components_provider = BudgetComponentsProvider(
            forecast_rule_provider=forecast_rule_provider,
            budget_repository=budget_repository,
            budget_agreement_provider=Mock(),
        )

        state = budget_components_provider.get_state(budget)

        assert state.budget_traffic_synchronized_at is None

    def test_when_full_calculation_does_not_exist(self):
        budget = BudgetFactory()
        budget_repository = InMemoryBudgetRepository(calculations=[])

        budget_components_provider = BudgetComponentsProvider(
            forecast_rule_provider=InMemoryForecastRuleProvider(),
            budget_repository=budget_repository,
            budget_agreement_provider=Mock(),
        )

        state = budget_components_provider.get_state(budget)

        assert state.forecast_rules_applied_at is None
