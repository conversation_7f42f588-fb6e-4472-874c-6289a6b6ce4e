import datetime
from datetime import date
from decimal import Decimal
from typing import Any, Type
from unittest.mock import Mock

import pytest
from rest_framework.exceptions import ValidationError
from rest_framework.fields import DateTimeField
from rest_framework.serializers import Serializer

from nga.apps.budgets.api.schemas import BudgetCalculationRetrieveSchema, BudgetParametersSchema
from nga.apps.budgets.api.serializers import (
    BudgetCalculationRetrieveSerializer,
    BudgetComponentsQuantitySerializer,
    BudgetComponentsStateSerializer,
    BudgetCreateSchemaSerializer,
    BudgetKPITotalChargesSerializer,
    BudgetKPITotalsSerializer,
    BudgetKPIValuesSerializer,
    BudgetListSerializer,
    BudgetParametersSchemaSerializer,
    CountryOperatorsValueRecordSerializer,
    CountryOperatorsValuesSerializer,
    ServiceTypeKPIValuesSerializer,
)
from nga.apps.budgets.api.serializers.budget_list import BudgetListQuerySerializer
from nga.apps.budgets.api.serializers.reporting import BudgetReportQuerySerializer, BudgetReportSerializer
from nga.apps.budgets.commands import CreateBudgetCommand
from nga.apps.budgets.domain.dto import BudgetComponentsQuantity
from nga.apps.budgets.enums import BudgetCalculationStatusEnum, BudgetCalculationTypeEnum, BudgetTypeEnum
from nga.apps.common.serializer_fields import MonthField, YearMonthField
from nga.apps.references.api.serializers import OperatorORMSerializer, OperatorSerializer
from nga.apps.users.models import User
from nga.apps.users.serializers import UserSerializer
from nga.core.enums import CallDestinationEnum, ServiceTypeEnum, TrafficDirectionEnum, TrafficTypeEnum
from nga.core.types import DatePeriod, Month
from nga.utils.dt import get_current_datetime_utc
from nga.utils.string import date_to_str
from tests.apps.references.fakes import InMemoryOperatorProvider
from tests.factories.budgets import (
    BudgetKPITotalChargesFactory,
    BudgetKPIValuesFactory,
    BudgetORMFactory,
    CountryOperatorsValuesFactory,
    CountryOperatorValueRecordFactory,
    ServiceTypeKPIValuesFactory,
)
from tests.factories.budgets.domain import BudgetComponentsStateFactory
from tests.factories.references import OperatorFactory, OperatorORMFactory


class TestBudgetListSerializer:
    serializer_class = BudgetListSerializer

    @pytest.mark.django_db
    def test_serialize_model_fields(self):
        budget = BudgetORMFactory(
            home_operators=[OperatorORMFactory()],
        )
        budget.lhm = budget.last_historical_month

        actual_data = self.serializer_class(budget).data

        expected_data = {
            "id": budget.id,
            "name": budget.name,
            "description": budget.description,
            "home_operators": OperatorORMSerializer(budget.home_operators, many=True).data,
            "start_date": date_to_str(budget.start_date),
            "end_date": date_to_str(budget.end_date),
            "last_historical_month": date_to_str(budget.last_historical_month),
            "is_master": budget.is_master,
            "type": BudgetTypeEnum(budget.type).name,
            "created_at": DateTimeField().to_representation(budget.created_at),
            "updated_at": DateTimeField().to_representation(budget.updated_at),
        }
        assert actual_data == expected_data

    def test_schema(self):
        expected_fields = (
            "id",
            "name",
            "description",
            "home_operators",
            "start_date",
            "end_date",
            "last_historical_month",
            "is_master",
            "type",
            "created_at",
            "updated_at",
        )
        s = self.serializer_class()
        assert sorted(s.fields.keys()) == sorted(expected_fields)


class TestBudgetListQuerySerializer:
    serializer_class = BudgetListQuerySerializer

    def test_schema(self):
        expected_fields = ("sort_field",)

        s = self.serializer_class()
        fields = sorted(s.fields.keys())

        assert tuple(fields) == tuple(expected_fields)

    def test_serialize(self):
        data = {
            "sort_field": "last_historical_month",
        }

        serializer_obj = self.serializer_class(data=data)
        serializer_obj.is_valid(raise_exception=True)
        validated_data = serializer_obj.validated_data

        assert validated_data["sort_field"] == "last_historical_month"


@pytest.mark.django_db
class TestBudgetKPIValuesSerializer:
    serializer_class = BudgetKPIValuesSerializer

    def test_schema(self):
        expected_fields = ("service_types", "total_charges")
        s = self.serializer_class()
        assert sorted(s.fields.keys()) == sorted(expected_fields)

    def test_total_charges(self):
        kpi_values = BudgetKPIValuesFactory()

        serializer = self.serializer_class(kpi_values)
        data = serializer.data

        assert "total_charges" in data
        totals = BudgetKPITotalChargesSerializer(kpi_values.total_charges).data
        assert data["total_charges"] == totals

        service_types = ServiceTypeKPIValuesSerializer(kpi_values.service_types, many=True).data
        assert "service_types" in data
        assert data["service_types"] == service_types


class TestBudgetKPITotalChargesSerializer:
    serializer_class = BudgetKPITotalChargesSerializer

    def test_schema(self):
        expected_fields = ("inbound", "outbound", "net_position")
        assert self.serializer_class.Meta.fields == expected_fields

    def test_serialize(self):
        totals = BudgetKPITotalChargesFactory()
        data = self.serializer_class(totals).data

        assert data["inbound"] == str(totals.inbound)
        assert data["outbound"] == str(totals.outbound)
        assert data["net_position"] == str(totals.net_position)


class TestServiceTypeKPIValuesSerializer:
    serializer_class = ServiceTypeKPIValuesSerializer

    def test_schema(self):
        expected_fields = (
            "service_type",
            "inbound_charge",
            "inbound_volume",
            "inbound_rate",
            "outbound_charge",
            "outbound_volume",
            "outbound_rate",
            "net_position_charge",
            "net_position_volume",
        )
        s = self.serializer_class()
        assert sorted(s.fields.keys()) == sorted(expected_fields)

    def test_serialize(self):
        values = ServiceTypeKPIValuesFactory()
        data = self.serializer_class(values).data

        assert data["inbound_charge"] == str(values.inbound_charge)
        assert data["inbound_rate"] == str(values.inbound_rate)
        assert data["inbound_volume"] == str(values.inbound_volume)

        assert data["net_position_charge"] == str(values.net_position_charge)
        assert data["net_position_volume"] == str(values.net_position_volume)

        assert data["outbound_charge"] == str(values.outbound_charge)
        assert data["outbound_volume"] == str(values.outbound_volume)
        assert data["outbound_rate"] == str(values.outbound_rate)

        assert data["service_type"] == values.service_type.name


class TestBudgetComponentsQuantitySerializer:
    serializer_class = BudgetComponentsQuantitySerializer

    def test_schema(self):
        expected_fields = (
            "budget_id",
            "agreements",
            "active_agreements",
            "forecast_rules",
            "iot_rates",
        )
        s = self.serializer_class()
        assert sorted(s.fields.keys()) == sorted(expected_fields)

    def test_serialize(self):
        totals = BudgetComponentsQuantity(
            budget_id=1,
            agreements=2,
            active_agreements=1,
            forecast_rules=1,
            iot_rates=1,
        )

        data = self.serializer_class(totals).data
        expected_data = {
            "budget_id": totals.budget_id,
            "agreements": totals.agreements,
            "active_agreements": totals.active_agreements,
            "forecast_rules": totals.forecast_rules,
            "iot_rates": totals.iot_rates,
        }
        assert data == expected_data


class TestBudgetKPITotalsSerializer:
    serializer_class = BudgetKPITotalsSerializer

    def test_schema(self):
        expected_fields = ("budget_id", "total_charges")
        s = self.serializer_class()
        assert sorted(s.fields.keys()) == sorted(expected_fields)

    def test_serialize(self):
        kpi_values = BudgetKPIValuesFactory()

        data = self.serializer_class(kpi_values).data

        expected_data = {
            "budget_id": kpi_values.options.budget_id,
            "total_charges": BudgetKPITotalChargesSerializer(kpi_values.total_charges).data,
        }
        assert data == expected_data


class TestCountryOperatorsValuesSerializer:
    serializer_class = CountryOperatorsValuesSerializer

    def test_schema(self):
        expected_fields = (
            "budget_id",
            "country_id",
            "agreement_id",
            "total_value",
            "total_previous_year_value",
            "total_country_share",
            "can_have_previous_year",
            "records",
        )
        s = self.serializer_class()
        assert sorted(s.fields.keys()) == sorted(expected_fields)

    def test_serialize(self):
        values = CountryOperatorsValuesFactory()
        actual_data = self.serializer_class(values).data

        assert actual_data["budget_id"] == values.options.budget_id
        assert actual_data["country_id"] == values.options.country_id
        assert actual_data["agreement_id"] == values.options.agreement_id

        records_serializer = CountryOperatorsValueRecordSerializer(
            values.records,
            many=True,
            context={"total_value": sum(v.value for v in values.records)},
        )
        assert actual_data["records"] == records_serializer.data

    def test_total_value(self):
        r1 = CountryOperatorValueRecordFactory()
        r2 = CountryOperatorValueRecordFactory()
        values = CountryOperatorsValuesFactory(records=[r1, r2])

        actual_data = self.serializer_class(values).data

        assert actual_data["total_value"] == str(r1.value + r2.value)

    def test_total_previous_year_value(self):
        r1 = CountryOperatorValueRecordFactory()
        r2 = CountryOperatorValueRecordFactory()
        values = CountryOperatorsValuesFactory(records=[r1, r2])

        actual_data = self.serializer_class(values).data

        expected_total_previous_year_value = str(r1.previous_year_value + r2.previous_year_value)
        assert actual_data["total_previous_year_value"] == expected_total_previous_year_value

    def test_country_share(self):
        values = CountryOperatorsValuesFactory()
        actual_data = self.serializer_class(values).data
        assert actual_data["total_country_share"] == "100.00"

    def test_can_have_previous_year_when_period_is_less_then_12_months(self):
        values = CountryOperatorsValuesFactory(options__period=DatePeriod(date(2022, 1, 1), date(2022, 12, 1)))
        actual_data = self.serializer_class(values).data
        assert actual_data["can_have_previous_year"] is True

    def test_can_have_previous_year_when_period_is_bigger_then_12_months(self):
        values = CountryOperatorsValuesFactory(options__period=DatePeriod(date(2022, 1, 1), date(2023, 12, 1)))
        actual_data = self.serializer_class(values).data
        assert actual_data["can_have_previous_year"] is False

    def test_when_options_period_is_none(self):
        values = CountryOperatorsValuesFactory(options__period=None)
        actual_data = self.serializer_class(values).data
        assert actual_data["can_have_previous_year"] is False


class TestCountryOperatorValueRecordSerializer:
    serializer_class = CountryOperatorsValueRecordSerializer

    def test_schema(self):
        expected_fields = (
            "operator_id",
            "value",
            "previous_year_value",
            "dynamic",
            "country_share",
        )
        s = self.serializer_class()
        assert sorted(s.fields.keys()) == sorted(expected_fields)

    def test_serialize(self):
        record = CountryOperatorValueRecordFactory(value=Decimal("100"))
        actual_data = self.serializer_class(record, context={"total_value": Decimal("150")}).data
        assert actual_data["operator_id"] == record.operator_id
        assert actual_data["value"] == "100.000000"
        assert actual_data["previous_year_value"] == str(record.previous_year_value)

    @pytest.mark.parametrize(
        "value,previous_year_value,expected_dynamic",
        [
            (Decimal("501.164"), Decimal("244.446"), "105.020332"),
            (Decimal("125.291"), Decimal("244.446"), "-48.744917"),
            (Decimal("122.223"), Decimal("122.223"), "0.000000"),
            (Decimal("0"), Decimal("1000"), "-100.000000"),
            (Decimal("12529.1"), Decimal("0"), "100.000000"),
        ],
    )
    def test_dynamic(
        self,
        # test parameters
        value: Decimal,
        previous_year_value: Decimal,
        expected_dynamic: str,
    ):
        record = CountryOperatorValueRecordFactory(
            value=value,
            previous_year_value=previous_year_value,
        )
        total_value = Decimal("100000")
        actual_data = self.serializer_class(record, context={"total_value": total_value}).data
        assert actual_data["dynamic"] == expected_dynamic

    @pytest.mark.parametrize(
        "value,total_value,expected_country_share",
        [
            (Decimal("30"), Decimal("90"), "33.33"),
            (Decimal("0"), Decimal("90"), "0.00"),
            (Decimal("0"), Decimal("0"), "0.00"),
        ],
    )
    def test_country_share(
        self,
        # test parameters
        value: Decimal,
        total_value: Decimal,
        expected_country_share: str,
    ):
        record = CountryOperatorValueRecordFactory(value=value)
        actual_data = self.serializer_class(record, context={"total_value": total_value}).data
        assert actual_data["country_share"] == expected_country_share


class TestBudgetCreateSchemaSerializer:
    serializer_class = BudgetCreateSchemaSerializer

    def test_schema(self):
        expected_fields = (
            "name",
            "description",
            "type",
            "home_operators",
            "start_date",
            "end_date",
            "last_historical_month",
            "user_id",
            "run_calculation",
        )

        s = self.serializer_class()
        fields = sorted(s.fields.keys())

        assert sorted(tuple(fields)) == sorted(tuple(expected_fields))

    @pytest.mark.parametrize(
        "budget_type, expected_is_valid",
        [
            (BudgetTypeEnum.MASTER, False),
            (BudgetTypeEnum.UPDATED, True),
            (BudgetTypeEnum.FROZEN, True),
            (BudgetTypeEnum.FROZEN_TRAFFIC, True),
        ],
    )
    def test_serialize(self, budget_type: BudgetTypeEnum, expected_is_valid: bool):
        operator = OperatorFactory()

        data = {
            "name": "test-name",
            "description": "test-description",
            "type": budget_type.name,
            "start_date": "2022-11",
            "end_date": "2022-12",
            "last_historical_month": "2022-11",
            "home_operators": [operator.id],
            "run_calculation": True,
        }

        request_mock = Mock()
        request_mock.user.pk = 23

        operator_provider = InMemoryOperatorProvider([operator])

        s = self.serializer_class(data=data, context={"request": request_mock}, operator_provider=operator_provider)
        is_valid = s.is_valid()

        assert is_valid == expected_is_valid

        if is_valid:
            cmd = s.save()

            assert cmd == CreateBudgetCommand(
                name=data["name"],
                description=data["description"],
                type=budget_type,
                home_operators=data["home_operators"],
                start_date=date(2022, 11, 1),
                end_date=date(2022, 12, 1),
                last_historical_month=Month.create_from_year_month(2022, 11),
                user_id=23,
                run_calculation=True,
            )

    def test_when_operator_id_does_not_exist(self):
        data = {
            "name": "test-name",
            "start_date": "2022-11",
            "end_date": "2022-12",
            "home_operators": [11],
        }

        operator_provider = InMemoryOperatorProvider([])

        s = self.serializer_class(data=data, context={"request": Mock()}, operator_provider=operator_provider)

        with pytest.raises(ValidationError) as exc_info:
            s.is_valid(raise_exception=True)

        assert "Operator" in exc_info.value.detail["home_operators"][0]

    def test_run_calculation_is_false_by_default(self):
        s = self.serializer_class()

        assert s.fields["run_calculation"].required is False
        assert s.fields["run_calculation"].default is False


class TestBudgetCalculationRetrieveSerializer:
    serializer_class = BudgetCalculationRetrieveSerializer

    def test_schema(self):
        expected_fields = (
            "id",
            "budget_id",
            "budget_agreement_id",
            "type",
            "status",
            "budget_lhm",
            "traffic_lhm",
            "distribution_lhm",
            "created_at",
            "finished_at",
            "traffic_synchronized_at",
            "forecast_rules_applied_at",
            "created_by",
        )
        s = self.serializer_class()
        fields = sorted(s.fields.keys())
        assert sorted(tuple(fields)) == sorted(tuple(expected_fields))

    def test_serialize(self):
        created_at = datetime.datetime.now(datetime.UTC)

        schema = BudgetCalculationRetrieveSchema(
            id=1,
            budget_id=2,
            budget_agreement_id=None,
            type=BudgetCalculationTypeEnum.FULL_WITH_TRAFFIC_UPDATE,
            status=BudgetCalculationStatusEnum.CALCULATED,
            created_by=User(id=1, email="a"),
            created_at=created_at,
            finished_at=None,
            traffic_synchronized_at=None,
            forecast_rules_applied_at=None,
            budget_lhm=Month.create_from_year_month(2025, 4),
            traffic_lhm=Month.create_from_year_month(2025, 6),
            distribution_lhm=Month.create_from_year_month(2025, 5),
        )

        data = self.serializer_class(schema).data

        assert data == {
            "id": schema.id,
            "budget_id": schema.budget_id,
            "budget_agreement_id": None,
            "type": "FULL_WITH_TRAFFIC_UPDATE",
            "status": "CALCULATED",
            "budget_lhm": MonthField().to_representation(schema.budget_lhm),
            "traffic_lhm": MonthField().to_representation(schema.traffic_lhm),
            "distribution_lhm": MonthField().to_representation(schema.distribution_lhm),
            "created_at": DateTimeField().to_representation(created_at),
            "created_by": UserSerializer(schema.created_by).data,
            "finished_at": None,
            "traffic_synchronized_at": None,
            "forecast_rules_applied_at": None,
        }


class TestBudgetParametersSchemaSerializer:
    serializer_class = BudgetParametersSchemaSerializer

    def test_schema(self):
        expected_fields = (
            "id",
            "name",
            "description",
            "start_date",
            "end_date",
            "last_historical_month",
            "home_operators",
            "is_master",
            "type",
            "created_at",
            "updated_at",
        )

        s = self.serializer_class()
        fields = sorted(s.fields.keys())

        assert sorted(tuple(fields)) == sorted(tuple(expected_fields))

    def test_serialize(self):
        hpmn = OperatorFactory()

        schema = BudgetParametersSchema(
            id=1,
            name="test",
            description="description",
            start_date=date(2022, 11, 1),
            end_date=date(2022, 12, 1),
            last_historical_month=Month.create_from_year_month(2022, 11),
            home_operators=[hpmn],
            is_master=False,
            type=BudgetTypeEnum.FROZEN,
            created_at=get_current_datetime_utc(),
            updated_at=get_current_datetime_utc(),
        )

        data = self.serializer_class(schema).data

        assert data == {
            "id": schema.id,
            "name": schema.name,
            "description": schema.description,
            "start_date": YearMonthField().to_representation(schema.start_date),
            "end_date": YearMonthField().to_representation(schema.end_date),
            "last_historical_month": MonthField().to_representation(schema.last_historical_month),
            "home_operators": OperatorSerializer([hpmn], many=True).data,
            "is_master": schema.is_master,
            "type": schema.type.name,
            "created_at": DateTimeField().to_representation(schema.created_at),
            "updated_at": DateTimeField().to_representation(schema.updated_at),
        }


class TestBudgetComponentsStateSerializer:
    serializer_class = BudgetComponentsStateSerializer

    def test_schema(self):
        expected_fields = (
            "budget_id",
            "historical_traffic_last_modified_at",
            "forecast_rules_last_modified_at",
            "agreements_last_modified_at",
            "budget_traffic_synchronized_at",
            "forecast_rules_applied_at",
            "budget_lhm",
            "traffic_lhm",
            "distribution_lhm",
        )
        s = self.serializer_class()
        assert sorted(s.fields.keys()) == sorted(tuple(expected_fields))

    def test_serialize(self):
        state = BudgetComponentsStateFactory(
            budget_lhm=Month.create_from_year_month(2025, 4),
            traffic_lhm=Month.create_from_year_month(2025, 6),
            distribution_lhm=Month.create_from_year_month(2025, 5),
        )

        data = self.serializer_class(state).data

        assert data == {
            "budget_id": state.budget_id,
            "historical_traffic_last_modified_at": DateTimeField().to_representation(
                state.historical_traffic_last_modified_at
            ),
            "forecast_rules_last_modified_at": DateTimeField().to_representation(state.forecast_rules_last_modified_at),
            "agreements_last_modified_at": DateTimeField().to_representation(state.agreements_last_modified_at),
            "budget_traffic_synchronized_at": DateTimeField().to_representation(state.budget_traffic_synchronized_at),
            "forecast_rules_applied_at": DateTimeField().to_representation(state.forecast_rules_applied_at),
            "budget_lhm": MonthField().to_representation(state.budget_lhm),
            "traffic_lhm": MonthField().to_representation(state.traffic_lhm),
            "distribution_lhm": MonthField().to_representation(state.distribution_lhm),
        }


class TestBudgetReportSerializer:
    serializer_class = BudgetReportSerializer

    def test_schema(self):
        expected_fields = (
            "home_operator_pmn",
            "partner_operator_pmn",
            "partner_country",
            "traffic_month",
            "traffic_type",
            "traffic_direction",
            "service_type",
            "call_destination",
            "called_country",
            "is_premium",
            "traffic_segment_name",
            "imsi_count_type",
            "volume_actual",
            "volume_billed",
            "tap_charge_net",
            "tap_charge_gross",
            "charge_net",
            "charge_gross",
            "discount_net",
            "discount_gross",
            "tap_rate_net_volume_actual",
            "tap_rate_gross_volume_actual",
            "discounted_rate_net_volume_actual",
            "discounted_rate_gross_volume_actual",
            "tap_rate_net_volume_billed",
            "tap_rate_gross_volume_billed",
            "discounted_rate_net_volume_billed",
            "discounted_rate_gross_volume_billed",
        )
        s = self.serializer_class()
        assert sorted(s.fields.keys()) == sorted(expected_fields)


class TestBudgetReportQuerySerializer:
    serializer_class = BudgetReportQuerySerializer

    data = {
        "columns": ["traffic_month", "traffic_direction", "service_type", "charge_gross"],
        "start_date": "2022-12",
        "end_date": "2022-12",
        "traffic_types": [TrafficTypeEnum.HISTORICAL.name, TrafficTypeEnum.FORECASTED.name],
        "traffic_directions": [TrafficDirectionEnum.INBOUND.name],
        "service_types": [ServiceTypeEnum.VOICE_MO.name, ServiceTypeEnum.VOICE_MT.name],
        "call_destinations": [CallDestinationEnum.HOME.name],
        "called_countries": [1],
        "is_premium": True,
        "traffic_segments": [5],
    }

    @classmethod
    def validate(
        cls,
        data: dict[str, Any],
        serializer_class: Type[Serializer] = serializer_class,
    ) -> None:
        s = serializer_class(data=data)
        s.is_valid(raise_exception=True)

    def test_validation_ok(self):
        s = self.serializer_class(data=self.data)
        s.is_valid(raise_exception=True)

    def test_validate_currency_is_optional(self):
        s = self.serializer_class(data=self.data)
        s.is_valid(raise_exception=True)

    def test_validate_columns_single_value(self):
        data = {
            **self.data,
            "columns": ["traffic_month"],
        }

        s = self.serializer_class(data=data)

        s.is_valid(raise_exception=True)

    def test_validate_columns_extra_columns(self):
        data = {
            **self.data,
            "columns": ["traffic_direction", "service_type", "charge_gross", "extra_column"],
        }
        s = self.serializer_class(data=data)

        with pytest.raises(ValidationError):
            s.is_valid(raise_exception=True)

    def test_validate_sort_field_missing_in_columns(self):
        columns = ["traffic_direction", "service_type", "charge_gross"]

        data = {
            **self.data,
            "columns": columns,
            "sort_field": "home_operator_pmn",
        }

        s = self.serializer_class(data=data)

        s.is_valid(raise_exception=True)
        serializer_data = s.data

        expected_data = {
            "home_operators": [],
            "partner_operators": [],
            "partner_countries": [],
            "columns": columns,
            "sort_field": None,
            "sort_sign": None,
            "start_date": "2022-12",
            "end_date": "2022-12",
            "traffic_types": [TrafficTypeEnum.HISTORICAL.name, TrafficTypeEnum.FORECASTED.name],
            "traffic_directions": [TrafficDirectionEnum.INBOUND.name],
            "service_types": [ServiceTypeEnum.VOICE_MO.name, ServiceTypeEnum.VOICE_MT.name],
            "call_destinations": [CallDestinationEnum.HOME.name],
            "called_countries": [1],
            "is_premium": True,
            "traffic_segments": [5],
        }

        assert serializer_data == expected_data

    def test_validate_sort_field_in_columns(self):
        columns = ["home_operator_pmn", "traffic_direction", "service_type", "charge_gross"]
        sort_field = "home_operator_pmn"

        data = {
            **self.data,
            "columns": columns,
            "sort_field": sort_field,
        }

        s = self.serializer_class(data=data)

        s.is_valid(raise_exception=True)

        serializer_data = s.data

        expected_data = {
            "home_operators": [],
            "partner_operators": [],
            "partner_countries": [],
            "columns": columns,
            "sort_field": sort_field,
            "sort_sign": None,
            "start_date": "2022-12",
            "end_date": "2022-12",
            "traffic_types": [TrafficTypeEnum.HISTORICAL.name, TrafficTypeEnum.FORECASTED.name],
            "traffic_directions": [TrafficDirectionEnum.INBOUND.name],
            "service_types": [ServiceTypeEnum.VOICE_MO.name, ServiceTypeEnum.VOICE_MT.name],
            "call_destinations": [CallDestinationEnum.HOME.name],
            "called_countries": [1],
            "is_premium": True,
            "traffic_segments": [5],
        }

        assert serializer_data == expected_data
