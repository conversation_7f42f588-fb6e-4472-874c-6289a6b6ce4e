from copy import copy, deepcopy
from dataclasses import asdict
from datetime import date, datetime
from typing import Collection, Iterable, Optional, Sequence

from nga.apps.agreements.domain.exceptions import BudgetAgreementDoesNotExist
from nga.apps.agreements.domain.models import BudgetAgreement
from nga.apps.budgets.api.mappers import AbstractBudgetParametersSchemaMapper
from nga.apps.budgets.api.schemas import BudgetParametersSchema
from nga.apps.budgets.domain import BudgetCalculationDoesNotExist
from nga.apps.budgets.domain.dto import (
    BudgetComponentsQuantity,
    BudgetComponentsState,
    BudgetParametersFilters,
    BudgetTrafficRecordDTO,
)
from nga.apps.budgets.domain.exceptions import BudgetDoesNotExist, BudgetValidationError
from nga.apps.budgets.domain.models import Budget, BudgetCalculation, BudgetTrafficRecord
from nga.apps.budgets.domain.repositories import AbstractBudgetRepository, AbstractBudgetTrafficRepository
from nga.apps.budgets.domain.specifications.budgets import AbstractBudgetSpecification
from nga.apps.budgets.enums import BudgetCalculationStatusEnum, BudgetCalculationTypeEnum, BudgetTypeEnum
from nga.apps.budgets.providers import AbstractBudgetComponentsProvider
from nga.apps.budgets.providers.budget import AbstractBudgetProvider
from nga.apps.budgets.providers.budget_components import AbstractBudgetAgreementProvider
from nga.apps.budgets.providers.forecast_rule import AbstractForecastRuleProvider
from nga.apps.calculation.budgets.snapshot_traffic_synchronizer import AbstractSnapshotTrafficSynchronizer
from nga.apps.calculation.discounts.external_service import AbstractExternalDiscountCalculationService
from nga.apps.forecasts.domain import ForecastRule
from nga.core.enums import CallDestinationEnum, ServiceTypeEnum, TrafficDirectionEnum, TrafficTypeEnum
from nga.core.types import DatePeriod, Month
from nga.utils.collections import to_id_list
from nga.utils.dt import get_current_datetime_utc
from tests.factories.budgets.domain import BudgetComponentsStateFactory, BudgetFactory


class InMemoryBudgetComponentsProvider(AbstractBudgetComponentsProvider):
    def __init__(self, state: Optional[BudgetComponentsState] = None) -> None:
        self.state = state

    def get_quantity(
        self,
        budget_id: int,
        filters: Optional[BudgetParametersFilters] = None,
    ) -> BudgetComponentsQuantity:
        return BudgetComponentsQuantity(
            budget_id=1,
            agreements=0,
            iot_rates=0,
            forecast_rules=0,
        )

    def get_state(self, budget: Budget) -> BudgetComponentsState:
        if self.state:
            return self.state

        return BudgetComponentsStateFactory()


class InMemoryForecastRuleProvider(AbstractForecastRuleProvider):
    def __init__(self, forecast_rules: Optional[list[ForecastRule]] = None) -> None:
        self.forecast_rules = forecast_rules or []

    def get_total_rules(
        self,
        budget_id: int,
        filters: Optional[BudgetParametersFilters] = None,
    ) -> int:
        return len(self.forecast_rules)


class InMemoryBudgetTrafficRepository(AbstractBudgetTrafficRepository):
    def __init__(self, records: Optional[list[BudgetTrafficRecord]] = None):
        self.records = records if records else []

    def get_many(
        self,
        snapshot_id: int,
        *,
        home_operators: Optional[Sequence[int]] = None,
        partner_operators: Optional[Sequence[int]] = None,
        traffic_types: Optional[Sequence[TrafficTypeEnum]] = None,
        traffic_months: Optional[Sequence[date]] = None,
        traffic_directions: Optional[Sequence[TrafficDirectionEnum]] = None,
        call_destinations: Optional[Sequence[CallDestinationEnum]] = None,
        service_types: Optional[Sequence[ServiceTypeEnum]] = None,
        period: Optional[DatePeriod] = None,
        include_forecasted: Optional[bool] = True,
        called_countries: Optional[Sequence[int]] = None,
        traffic_segments: Optional[Sequence[int]] = None,
        include_satellite: Optional[bool] = True,
    ) -> Iterable[BudgetTrafficRecord]:

        if period:
            traffic_months = list(period)
        else:
            traffic_months = traffic_months or []

        records = [*self.records]

        if snapshot_id:
            records = [r for r in records if r.budget_snapshot_id == snapshot_id]

        if home_operators:
            records = [r for r in records if r.home_operator_id in home_operators]

        if partner_operators:
            records = [r for r in records if r.partner_operator_id in partner_operators]

        if traffic_months:
            records = [r for r in records if r.traffic_month in traffic_months]

        if traffic_types:
            records = [r for r in records if r.traffic_type in traffic_types]

        if traffic_directions:
            records = [r for r in records if r.traffic_direction in traffic_directions]

        if service_types:
            records = [r for r in records if r.service_type in service_types]

        if call_destinations:
            records = [r for r in records if r.call_destination in call_destinations]

        return (copy(r) for r in records)

    def create_many(self, record_dtos: Iterable[BudgetTrafficRecordDTO]) -> None:
        new_records = [
            BudgetTrafficRecord(
                id=int(get_current_datetime_utc().timestamp()),
                budget_snapshot_id=dto.budget_snapshot_id,
                home_operator_id=dto.home_operator_id,
                partner_operator_id=dto.partner_operator_id,
                traffic_segment_id=dto.traffic_segment_id,
                traffic_direction=dto.traffic_direction,
                traffic_month=dto.traffic_month,
                traffic_type=dto.traffic_type,
                service_type=dto.service_type,
                call_destination=dto.call_destination,
                called_country_id=dto.called_country_id,
                is_premium=dto.is_premium,
                imsi_count_type=dto.imsi_count_type,
                forecast_rule_id=dto.forecast_rule_id,
                discount_id=dto.discount_id,
                volume_actual=dto.volume_actual,
                volume_billed=dto.volume_billed,
                charge_net=dto.charge_net,
                charge_gross=dto.charge_gross,
                tap_charge_net=dto.tap_charge_net,
                tap_charge_gross=dto.tap_charge_gross,
            )
            for dto in record_dtos
        ]

        self.records.extend(new_records)

    def delete_many(
        self,
        budget_snapshot_id: int,
        *,
        ids: Optional[Collection[int]] = None,
        home_operators: Optional[Sequence[int]] = None,
        partner_operators: Optional[Sequence[int]] = None,
        traffic_months: Optional[Sequence[Month]] = None,
        traffic_type: Optional[TrafficTypeEnum] = None,
        forecast_rule_id: Optional[int] = None,
        discount_ids: Optional[Sequence[int]] = None,
    ) -> None:
        """Removes records only by snapshot ID."""

        self.records = [r for r in self.records if r.budget_snapshot_id != budget_snapshot_id]

        if ids:
            self.records = [r for r in self.records if r.id not in ids]

    def update_many(self, records: Collection[BudgetTrafficRecord]) -> None:
        self.records = [r for r in self.records if r.id not in to_id_list(records)]
        self.records.extend(records)

    def update_or_create(self, record: BudgetTrafficRecordDTO) -> None:
        records = [
            r
            for r in self.records
            if r.budget_snapshot_id == record.budget_snapshot_id
            and r.home_operator_id == record.home_operator_id
            and r.partner_operator_id == record.partner_operator_id
            and r.traffic_type == record.traffic_type
            and r.traffic_month == record.traffic_month
            and r.traffic_direction == record.traffic_direction
            and r.traffic_segment_id == record.traffic_segment_id
            and r.service_type == record.service_type
            and r.call_destination == record.call_destination
            and r.called_country_id == record.called_country_id
            and r.is_premium == record.is_premium
        ]

        if records:
            update_record = BudgetTrafficRecord(
                id=records[0].id,
                budget_snapshot_id=record.budget_snapshot_id,
                home_operator_id=record.home_operator_id,
                partner_operator_id=record.partner_operator_id,
                traffic_segment_id=record.traffic_segment_id,
                traffic_direction=record.traffic_direction,
                traffic_month=record.traffic_month,
                traffic_type=record.traffic_type,
                service_type=record.service_type,
                call_destination=record.call_destination,
                called_country_id=record.called_country_id,
                is_premium=record.is_premium,
                forecast_rule_id=record.forecast_rule_id,
                discount_id=record.discount_id,
                volume_actual=record.volume_actual,
                volume_billed=record.volume_billed,
                charge_net=record.charge_net,
                charge_gross=record.charge_gross,
                tap_charge_net=record.tap_charge_net,
                tap_charge_gross=record.tap_charge_gross,
                imsi_count_type=record.imsi_count_type,
            )

            self.update_many([update_record])
        else:
            self.create_many([record])

    def exists(
        self,
        *,
        budget_snapshot_id: int,
        home_operators: Sequence[int],
        partner_operators: Sequence[int],
        period: DatePeriod,
        traffic_direction: TrafficDirectionEnum,
        service_type: ServiceTypeEnum,
    ) -> bool:
        records = self.get_many(
            snapshot_id=budget_snapshot_id,
            home_operators=home_operators,
            partner_operators=partner_operators,
            traffic_months=list(period),
            traffic_directions=[traffic_direction],
            service_types=[service_type],
        )

        return len(list(records)) > 0

    def copy_many(
        self,
        source_snapshot_id: int,
        target_snapshot_id: int,
        *,
        home_operators: Optional[Sequence[int]] = None,
        partner_operators: Optional[Sequence[int]] = None,
        traffic_months: Optional[Sequence[Month]] = None,
    ) -> None:
        records_to_copy = [r for r in self.records if r.budget_snapshot_id == source_snapshot_id]

        if home_operators:
            records_to_copy = [r for r in records_to_copy if r.home_operator_id in home_operators]

        if partner_operators:
            records_to_copy = [r for r in records_to_copy if r.partner_operator_id in partner_operators]

        if traffic_months:
            records_to_copy = [r for r in records_to_copy if r.traffic_month in traffic_months]

        copied_records = [
            BudgetTrafficRecord(**{**asdict(r), "budget_snapshot_id": target_snapshot_id}) for r in records_to_copy
        ]

        self.records.extend(copied_records)

    def get_last_historical_month(self, snapshot_id: int) -> Optional[Month]:
        try:
            return next(
                Month.create_from_date(r.traffic_month)
                for r in self.records
                if r.budget_snapshot_id == snapshot_id and r.traffic_type == TrafficTypeEnum.HISTORICAL
            )
        except StopIteration:
            return None

    def reset_discount(
        self,
        *,
        snapshot_id: Optional[int] = None,
        home_operators: Optional[Sequence[int]] = None,
        partner_operators: Optional[Sequence[int]] = None,
        period: Optional[DatePeriod] = None,
    ) -> None:
        records = [r for r in self.records if r.budget_snapshot_id == snapshot_id]

        if home_operators:
            records = [r for r in records if r.home_operator_id in home_operators]

        if partner_operators:
            records = [r for r in records if r.partner_operator_id in partner_operators]

        if period:
            records = [r for r in records if Month.create_from_date(r.traffic_month) in period]

        for r in records:
            r.charge_net = r.tap_charge_net
            r.charge_gross = r.tap_charge_gross


class InMemoryBudgetRepository(AbstractBudgetRepository):
    def __init__(self, budgets: list[Budget] = None, calculations: list[BudgetCalculation] = None) -> None:
        self.budgets = budgets if budgets else []
        self.calculations = calculations if calculations else []

        self.delete_markers: dict[int, bool] = {}

    def get_by_id(self, budget_id: int) -> Budget:
        try:
            return next(b for b in self.budgets if b.id == budget_id)
        except StopIteration:
            raise BudgetDoesNotExist.from_budget_id(budget_id)

    def get_by_snapshot_id(self, budget_snapshot_id: int) -> Budget:
        return next(
            b
            for b in self.budgets
            if b.active_snapshot_id == budget_snapshot_id or b.calculation_snapshot_id == budget_snapshot_id
        )

    def get_many(
        self,
        home_operators: Optional[Sequence[int]] = None,
        month_included_in_period: Optional[date] = None,
    ) -> tuple[Budget, ...]:

        budgets = [*self.budgets]

        if home_operators:
            budgets = [b for b in budgets if set(b.home_operators).intersection(set(home_operators))]

        return tuple(budgets)

    def get_master(self) -> Budget:
        for budget in self.budgets:
            if budget.is_master:
                return budget

    def create(
        self,
        name: str,
        period: DatePeriod,
        type: BudgetTypeEnum,
        home_operators: list[int],
        last_historical_month: Month,
        *,
        user_id: Optional[int],
        description: Optional[str],
    ) -> Budget:
        budget = BudgetFactory(
            name=name,
            description=description,
            type=type,
            period=period,
            home_operators=home_operators,
            is_master=False,
            last_historical_month=last_historical_month,
            forecast_rules_modified_at=None,
            historical_traffic_modified_at=None,
            agreements_last_modified_at=None,
        )

        self.budgets.append(budget)

        return budget

    def save(self, budget: Budget) -> Budget:
        self.budgets = [b for b in self.budgets if b.id != budget.id]
        self.budgets.append(budget)

        return budget

    def get_last_calculation(self, budget_id: int) -> BudgetCalculation:
        calculations = sorted(self.calculations, key=lambda c: c.created_at)

        if len(calculations) == 0:
            raise BudgetCalculationDoesNotExist.from_budget_id(budget_id)

        return calculations[-1]

    def get_last_agreement_calculation(self, budget_id: int, budget_agreement_id: int) -> BudgetCalculation:
        calculations = [
            c
            for c in self.calculations
            if c.budget_id == budget_id
            and c.budget_agreement_id == budget_agreement_id
            and c.type == BudgetCalculationTypeEnum.SINGLE_AGREEMENT
        ]

        calculations = sorted(calculations, key=lambda c: c.created_at)

        if not calculations:
            raise BudgetCalculationDoesNotExist.from_budget_id(budget_id)

        return calculations[-1]

    def get_last_full_calculation(self, budget_id: int) -> BudgetCalculation:
        calculations = sorted(self.calculations, key=lambda c: c.created_at)
        calculations = [
            c
            for c in calculations
            if c.type
            in (
                BudgetCalculationTypeEnum.FULL_WITH_TRAFFIC_UPDATE,
                BudgetCalculationTypeEnum.FULL_WITHOUT_TRAFFIC_UPDATE,
            )
        ]

        if not calculations:
            raise BudgetCalculationDoesNotExist.from_budget_id(budget_id)

        return calculations[-1]

    def get_last_full_traffic_update_calculation(self, budget_id: int) -> BudgetCalculation:
        calculations = sorted(self.calculations, key=lambda c: c.created_at)
        calculations = [c for c in calculations if c.type == BudgetCalculationTypeEnum.FULL_WITH_TRAFFIC_UPDATE]

        if not calculations:
            raise BudgetCalculationDoesNotExist.from_budget_id(budget_id)

        return calculations[-1]

    def create_calculation(
        self,
        budget_id: int,
        calculation_type: BudgetCalculationTypeEnum,
        created_by_user_id: Optional[int],
        budget_agreement_id: Optional[int],
        budget_lhm: Month,
        traffic_lhm: Optional[Month],
        distribution_lhm: Optional[Month],
    ) -> BudgetCalculation:
        budget = self.get_by_id(budget_id)

        calculation = BudgetCalculation(
            id=1,
            budget_id=budget_id,
            budget_snapshot_id=budget.calculation_snapshot_id,
            type=calculation_type,
            status=BudgetCalculationStatusEnum.WAITING_FOR_START,
            budget_agreement_id=budget_agreement_id,
            created_by_user_id=created_by_user_id,
            created_at=datetime.now(),
            finished_at=None,
            traffic_synchronized_at=None,
            forecast_rules_applied_at=None,
            agreements_applied_at=None,
            budget_lhm=budget_lhm,
            traffic_lhm=traffic_lhm,
            distribution_lhm=distribution_lhm,
            job_id=None,
        )
        self.calculations.append(deepcopy(calculation))

        return calculation

    def save_calculation(self, calculation: BudgetCalculation) -> BudgetCalculation:
        updated_calculation = deepcopy(calculation)

        self.calculations = [c for c in self.calculations if c.id != calculation.id]
        self.calculations.append(updated_calculation)

        return updated_calculation

    def get_calculation_by_id(self, calculation_id: int) -> BudgetCalculation:
        return next(c for c in self.calculations if c.id == calculation_id)

    def set_for_delete(self, budget_id: int) -> None:
        self.delete_markers[budget_id] = True

    def delete_by_id(self, budget_id: int) -> None:
        self.budgets = [b for b in self.budgets if b.id != budget_id]


class InMemoryBudgetProvider(AbstractBudgetProvider):
    def __init__(self, budgets: Optional[list[Budget]] = None):
        self.budgets = budgets if budgets else []

    def get_by_id(self, budget_id: int) -> Budget:
        try:
            return next(b for b in self.budgets if b.id == budget_id)
        except StopIteration:
            raise BudgetDoesNotExist.from_budget_id(budget_id)


class FakeBudgetSpecification(AbstractBudgetSpecification):
    def __init__(self):
        self.verified = False

    def verify(self, budget: Budget) -> None:
        self.verified = True


class ErrorBudgetSpecification(AbstractBudgetSpecification):
    def verify(self, budget: Budget) -> None:
        raise BudgetValidationError("fake error.")


class FakeExternalDiscountCalculationService(AbstractExternalDiscountCalculationService):
    def __init__(self):
        self.calculated_traffic_applied = False

    def apply_discounts(self, budget: Budget) -> None:
        self.calculated_traffic_applied = True


class FakeBudgetTrafficSyncService(AbstractSnapshotTrafficSynchronizer):
    def __init__(self):
        self.traffic_synchronized = False
        self.calculation_snapshot_synchronized = False
        self.active_snapshot_synchronized = False
        self.active_snapshot_agreement_synchronized = False

    def sync_historical_traffic(self, budget: Budget) -> None:
        self.traffic_synchronized = True

    def sync_calculation_with_active(self, budget: Budget) -> None:
        self.calculation_snapshot_synchronized = True

    def sync_active_with_calculation(self, budget: Budget) -> None:
        self.active_snapshot_synchronized = True

    def sync_agreements_traffic_with_active(
        self,
        budget: Budget,
        budget_agreements: Sequence[BudgetAgreement],
    ) -> None:
        self.active_snapshot_agreement_synchronized = True


class FakeBudgetParametersSchemaMapper(AbstractBudgetParametersSchemaMapper):
    def __init__(self, schema: BudgetParametersSchema):
        self._schema = schema

    def map(self, budget: Budget) -> BudgetParametersSchema:
        return self._schema


class InMemoryBudgetAgreementProvider(AbstractBudgetAgreementProvider):
    def __init__(self, agreements: Optional[list[BudgetAgreement]] = None) -> None:
        self.agreements = agreements or []

    def get_by_id(self, budget_agreement_id: int) -> BudgetAgreement:
        try:
            return copy(next(a for a in self.agreements if a.id == budget_agreement_id))
        except StopIteration:
            raise BudgetAgreementDoesNotExist(budget_agreement_id)

    def get_total_agreements(
        self,
        budget_id: int,
        *,
        budget_parameters: Optional[BudgetParametersFilters] = None,
        is_active: Optional[bool] = None,
    ) -> int:
        return len(self.agreements)

    def get_many(
        self,
        *,
        budget_id: Optional[int] = None,
        agreement_ids: Optional[list[int]] = None,
        only_active: Optional[bool] = None,
        only_modified: Optional[bool] = None,
    ) -> tuple[BudgetAgreement, ...]:
        agreements = [*self.agreements]

        if budget_id:
            agreements = [a for a in agreements if a.budget_id == budget_id]

        if agreement_ids:
            agreements = [a for a in agreements if a.id in agreement_ids]

        return tuple(copy(a) for a in agreements)

    def update_many(self, budget_agreements: Sequence[BudgetAgreement]) -> None:
        ba_ids = to_id_list(budget_agreements)

        self.agreements = [ba for ba in self.agreements if ba.id not in ba_ids]
        self.agreements.extend([copy(ba) for ba in budget_agreements])
