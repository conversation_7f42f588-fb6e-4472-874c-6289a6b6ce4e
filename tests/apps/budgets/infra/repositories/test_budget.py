from datetime import date, datetime, timezone

import pytest

from nga.apps.agreements.infra.orm.models import BudgetAgreement
from nga.apps.budgets.domain import (
    Budget,
    BudgetCalculation,
    BudgetCalculationDoesNotExist,
    BudgetCalculationIsRunning,
    BudgetSnapshot,
)
from nga.apps.budgets.domain.exceptions import BudgetDoesNotExist
from nga.apps.budgets.enums import (
    BudgetCalculationStatusEnum,
    BudgetCalculationTypeEnum,
    BudgetSnapshotTypeEnum,
    BudgetTypeEnum,
)
from nga.apps.budgets.infra.orm import models
from nga.apps.budgets.infra.repositories import BudgetDjangoORMRepository
from nga.apps.budgets.infra.repositories.orm_budget import (
    BUDGET_SNAPSHOT_SYSTEM_NAME,
    from_orm_budget_to_domain,
    from_orm_calculation_to_domain,
)
from nga.apps.common.queryset_utils import to_pk_list
from nga.apps.forecasts.infra.orm.models import ForecastRule
from nga.core.types import DatePeriod, Month
from nga.utils.dt import get_current_datetime_utc
from tests.factories.agreements import BudgetAgreementORMFactory
from tests.factories.budgets import (
    BudgetCalculationORMFactory,
    BudgetORMFactory,
    BudgetSnapshotORMFactory,
    BudgetTrafficRecordORMFactory,
    CalculationBudgetSnapshotORMFactory,
    MasterBudgetORMFactory,
)
from tests.factories.forecasts import ForecastRuleORMFactory
from tests.factories.references import OperatorORMFactory
from tests.factories.users import UserORMFactory


@pytest.fixture
def _repository() -> BudgetDjangoORMRepository:
    return BudgetDjangoORMRepository()


@pytest.mark.django_db
class TestBudgetORMRepository:
    def test_get_by_id_when_budget_does_not_exist(self, _repository):
        with pytest.raises(BudgetDoesNotExist):
            _repository.get_by_id(budget_id=1)

    def test_get_by_id(self, _repository):
        orm_budget = BudgetORMFactory()

        budget = _repository.get_by_id(orm_budget.id)

        assert budget.id == orm_budget.id
        assert budget.name == orm_budget.name
        assert budget.is_master == orm_budget.is_master
        assert budget.period == DatePeriod(orm_budget.start_date, orm_budget.end_date)
        assert budget.home_operators == to_pk_list(orm_budget.home_operators.all())
        assert budget.active_snapshot_id == orm_budget.get_active_snapshot_id()
        assert budget.calculation_snapshot_id == orm_budget.get_calculation_snapshot_id()
        assert budget.last_historical_month == Month.create_from_date(orm_budget.last_historical_month)

        assert orm_budget.forecast_rules_modified_at is not None
        assert budget.forecast_rules_modified_at == orm_budget.forecast_rules_modified_at

        assert orm_budget.historical_traffic_modified_at is not None
        assert budget.historical_traffic_modified_at == orm_budget.historical_traffic_modified_at

    def test_get_many_empty(self, _repository):
        orm_budgets = []
        budgets = _repository.get_many()

        assert len(budgets) == len(orm_budgets)

    def test_get_many_ok(self, _repository):
        orm_budgets = [BudgetORMFactory(), BudgetORMFactory(), BudgetORMFactory()]
        orm_budgets.sort(key=lambda b: b.id)

        budgets = _repository.get_many()
        budgets = sorted(budgets, key=lambda b: b.id)

        assert len(budgets) == len(orm_budgets)
        for actual, expected in zip(budgets, orm_budgets):
            assert actual.id == expected.id
            assert actual.name == expected.name
            assert actual.is_master == expected.is_master
            assert actual.period == DatePeriod(expected.start_date, expected.end_date)
            assert actual.home_operators == to_pk_list(expected.home_operators.all())
            assert actual.active_snapshot_id == expected.get_active_snapshot_id()
            assert actual.calculation_snapshot_id == expected.get_calculation_snapshot_id()
            assert actual.last_historical_month == Month.create_from_date(expected.last_historical_month)

            assert expected.forecast_rules_modified_at is not None
            assert actual.forecast_rules_modified_at == expected.forecast_rules_modified_at

            assert expected.historical_traffic_modified_at is not None
            assert actual.historical_traffic_modified_at == expected.historical_traffic_modified_at

    def test_get_many_filter_by_month(self, _repository):
        budget_factory = BudgetORMFactory

        expected_budget = budget_factory(start_date=date(2022, 2, 1), end_date=date(2023, 1, 1))

        budget_factory(start_date=date(2020, 1, 1), end_date=date(2022, 1, 1))
        budget_factory(start_date=date(2018, 1, 1), end_date=date(2020, 1, 1))

        budgets = _repository.get_many(month_included_in_period=date(2022, 5, 1))

        assert len(budgets) == 1

        actual_budget = budgets[0]

        assert actual_budget.id == expected_budget.id
        assert actual_budget.name == expected_budget.name
        assert actual_budget.is_master == expected_budget.is_master
        assert actual_budget.period == DatePeriod(expected_budget.start_date, expected_budget.end_date)
        assert actual_budget.home_operators == to_pk_list(expected_budget.home_operators.all())
        assert actual_budget.active_snapshot_id == expected_budget.get_active_snapshot_id()
        assert actual_budget.calculation_snapshot_id == expected_budget.get_calculation_snapshot_id()
        assert actual_budget.last_historical_month == expected_budget.last_historical_month
        assert actual_budget.forecast_rules_modified_at == expected_budget.forecast_rules_modified_at
        assert actual_budget.historical_traffic_modified_at == expected_budget.historical_traffic_modified_at

    def test_get_many_filter_with_lhm_filter_by_month(self, _repository):
        start_date = date(2022, 2, 1)

        expected_budget = BudgetORMFactory(
            start_date=start_date, last_historical_month=date(2023, 11, 1), end_date=date(2024, 1, 1)
        )

        BudgetORMFactory(
            start_date=start_date,
            last_historical_month=date(2023, 6, 1),
            end_date=date(2024, 10, 1),
        )

        budgets = _repository.get_many(month_included_in_period=date(2023, 11, 1))

        assert len(budgets) == 1

        actual_budget = budgets[0]

        assert actual_budget.id == expected_budget.id
        assert actual_budget.name == expected_budget.name
        assert actual_budget.is_master == expected_budget.is_master
        assert actual_budget.period == DatePeriod(expected_budget.start_date, expected_budget.end_date)
        assert actual_budget.home_operators == to_pk_list(expected_budget.home_operators.all())
        assert actual_budget.active_snapshot_id == expected_budget.get_active_snapshot_id()
        assert actual_budget.calculation_snapshot_id == expected_budget.get_calculation_snapshot_id()
        assert actual_budget.last_historical_month == expected_budget.last_historical_month
        assert actual_budget.forecast_rules_modified_at == expected_budget.forecast_rules_modified_at
        assert actual_budget.historical_traffic_modified_at == expected_budget.historical_traffic_modified_at

    def test_get_many_filter_by_home_operators(self, _repository):
        hm = OperatorORMFactory()
        hm_2 = OperatorORMFactory()
        hm_3 = OperatorORMFactory()

        expected_orm_budgets = [
            BudgetORMFactory(home_operators=[hm]),
            BudgetORMFactory(home_operators=[hm, hm_2]),
            BudgetORMFactory(home_operators=[hm, hm_2, hm_3]),
        ]
        expected_orm_budgets.sort(key=lambda b: b.id)

        BudgetORMFactory.create_batch(size=10)

        budgets = _repository.get_many(home_operators=[hm.id, hm_2.id])
        budgets = sorted(budgets, key=lambda b: b.id)

        assert len(budgets) == len(expected_orm_budgets)

        for actual, expected in zip(budgets, expected_orm_budgets):
            assert actual.id == expected.id
            assert actual.name == expected.name
            assert actual.is_master == expected.is_master
            assert actual.period == DatePeriod(expected.start_date, expected.end_date)
            assert actual.home_operators == to_pk_list(expected.home_operators.all())
            assert actual.active_snapshot_id == expected.get_active_snapshot_id()
            assert actual.calculation_snapshot_id == expected.get_calculation_snapshot_id()
            assert actual.last_historical_month == Month.create_from_date(expected.last_historical_month)

            assert expected.forecast_rules_modified_at is not None
            assert actual.forecast_rules_modified_at == expected.forecast_rules_modified_at

            assert expected.historical_traffic_modified_at is not None
            assert actual.historical_traffic_modified_at == expected.historical_traffic_modified_at

    def test_get_many_that_are_not_deleting(self, _repository):
        existing_budget = BudgetORMFactory(_is_deleting=None)

        BudgetORMFactory(_is_deleting=True)
        BudgetORMFactory(_is_deleting=True)

        domain_budgets = _repository.get_many()

        assert len(domain_budgets) == 1

        assert domain_budgets[0].id == existing_budget.pk

    def test_get_master_when_it_does_not_exist(self, _repository):
        with pytest.raises(BudgetDoesNotExist) as exc_info:
            _repository.get_master()

        assert "Master" in exc_info.value.message

    def test_get_master(self, _repository):
        master_orm_budget = MasterBudgetORMFactory()

        master_budget = _repository.get_master()
        assert master_budget.id == master_orm_budget.pk
        assert master_budget.is_master is True


@pytest.mark.django_db
class TestBudgetSave:
    def test_active_snapshot_id_is_saved(self, _repository: BudgetDjangoORMRepository):
        orm_budget = BudgetORMFactory()
        snapshot = BudgetSnapshotORMFactory()

        assert orm_budget.active_snapshot_id != snapshot.id

        budget = from_orm_budget_to_domain(orm_budget)

        budget.active_snapshot_id = snapshot.id

        updated_budget = _repository.save(budget)

        assert updated_budget.active_snapshot_id == snapshot.id

    def test_budget_components_state_modification_dates_are_saved(self, _repository: BudgetDjangoORMRepository):
        orm_budget = BudgetORMFactory(forecast_rules_modified_at=None, historical_traffic_modified_at=None)

        budget = from_orm_budget_to_domain(orm_budget)

        assert budget.forecast_rules_modified_at is None
        assert budget.historical_traffic_modified_at is None

        budget.forecast_rules_modified_at = get_current_datetime_utc()
        budget.historical_traffic_modified_at = get_current_datetime_utc()

        updated_budget = _repository.save(budget)

        assert isinstance(updated_budget.forecast_rules_modified_at, datetime) is True
        assert isinstance(updated_budget.historical_traffic_modified_at, datetime) is True

    def test_save_budget_name(self, _repository: BudgetDjangoORMRepository):
        orm_budget = BudgetORMFactory()

        budget = from_orm_budget_to_domain(orm_budget)
        budget.name = "updated name"

        updated_budget = _repository.save(budget)
        assert updated_budget.name == "updated name"

    def test_save_budget_description(self, _repository: BudgetDjangoORMRepository):
        orm_budget = BudgetORMFactory()

        budget = from_orm_budget_to_domain(orm_budget)
        budget.description = "updated description"

        updated_budget = _repository.save(budget)
        assert updated_budget.description == "updated description"

    def test_save_budget_type(self, _repository: BudgetDjangoORMRepository):
        orm_budget = BudgetORMFactory(type=BudgetTypeEnum.UPDATED)

        budget = from_orm_budget_to_domain(orm_budget)
        budget.type = BudgetTypeEnum.FROZEN

        updated_budget = _repository.save(budget)
        assert updated_budget.type == BudgetTypeEnum.FROZEN

    def test_save_last_historical_month(self, _repository: BudgetDjangoORMRepository):
        orm_budget = BudgetORMFactory()

        budget = from_orm_budget_to_domain(orm_budget)
        budget.last_historical_month = Month.create_from_year_month(2023, 9)

        updated_budget = _repository.save(budget)
        assert updated_budget.last_historical_month == Month.create_from_year_month(2023, 9)

    def test_save_start_date(self, _repository: BudgetDjangoORMRepository):
        start_date = date(2022, 10, 1)

        orm_budget = BudgetORMFactory()

        budget = from_orm_budget_to_domain(orm_budget)
        budget.period.start_date = start_date

        updated_budget = _repository.save(budget)
        assert updated_budget.period.start_date == start_date

    def test_save_end_date(self, _repository: BudgetDjangoORMRepository):
        end_date = date(2030, 12, 31)

        orm_budget = BudgetORMFactory()

        budget = from_orm_budget_to_domain(orm_budget)
        budget.period.end_date = end_date

        updated_budget = _repository.save(budget)
        assert updated_budget.period.end_date == end_date


@pytest.mark.django_db
class TestGetLastCalculation:
    def test_get_when_calculation_exists(self, _repository: BudgetDjangoORMRepository):
        orm_calc_snapshot = CalculationBudgetSnapshotORMFactory()
        orm_budget = orm_calc_snapshot.budget

        BudgetCalculationORMFactory(budget_snapshot=orm_calc_snapshot)
        BudgetCalculationORMFactory(budget_snapshot=orm_calc_snapshot)
        orm_calc = BudgetCalculationORMFactory(budget_snapshot=orm_calc_snapshot)

        result_calc = _repository.get_last_calculation(orm_budget.id)

        expected_calc = BudgetCalculation(
            id=orm_calc.id,
            budget_id=orm_budget.id,
            budget_snapshot_id=orm_calc_snapshot.id,
            type=BudgetCalculationTypeEnum(orm_calc.type),
            status=BudgetCalculationStatusEnum(orm_calc.status),
            budget_agreement_id=None,
            created_by_user_id=orm_calc.created_by_id,
            created_at=orm_calc.created_at,
            finished_at=orm_calc.finished_at,
            traffic_synchronized_at=orm_calc.traffic_synchronized_at,
            forecast_rules_applied_at=orm_calc.forecast_rules_applied_at,
            agreements_applied_at=orm_calc.agreements_applied_at,
            budget_lhm=orm_calc.budget_lhm,
            traffic_lhm=orm_calc.traffic_lhm,
            distribution_lhm=orm_calc.distribution_lhm,
            job_id=orm_calc.job_id,
        )

        assert result_calc == expected_calc

    def test_when_budget_has_not_been_calculated(self, _repository: BudgetDjangoORMRepository):
        orm_calc_snapshot = CalculationBudgetSnapshotORMFactory()
        orm_budget = orm_calc_snapshot.budget

        # noise calculations
        BudgetCalculationORMFactory.create_batch(size=3, budget_snapshot__type=BudgetSnapshotTypeEnum.CALCULATION)

        with pytest.raises(BudgetCalculationDoesNotExist) as exc_info:
            _repository.get_last_calculation(orm_budget.id)

        assert str(orm_budget.id) in exc_info.value.message


@pytest.mark.django_db
class TestCreateCalculation:
    def test_ok(self, _repository):
        orm_calc_snapshot = CalculationBudgetSnapshotORMFactory()

        orm_user = UserORMFactory()

        orm_budget_agreement = BudgetAgreementORMFactory()

        budget_lhm = Month.create_from_year_month(2025, 5)
        traffic_lhm = Month.create_from_year_month(2025, 6)
        distribution_lhm = Month.create_from_year_month(2025, 7)

        calculation = _repository.create_calculation(
            budget_id=orm_calc_snapshot.budget_id,
            calculation_type=BudgetCalculationTypeEnum.FULL_WITH_TRAFFIC_UPDATE,
            created_by_user_id=orm_user.id,
            budget_agreement_id=orm_budget_agreement.id,
            budget_lhm=budget_lhm,
            traffic_lhm=traffic_lhm,
            distribution_lhm=distribution_lhm,
        )
        assert isinstance(calculation, BudgetCalculation)

        orm_calculation = models.BudgetCalculation.objects.get(pk=calculation.id)

        assert calculation.id == orm_calculation.id
        assert calculation.budget_id == orm_calc_snapshot.budget_id
        assert calculation.budget_snapshot_id == orm_calc_snapshot.id
        assert calculation.type == BudgetCalculationTypeEnum.FULL_WITH_TRAFFIC_UPDATE
        assert calculation.status == BudgetCalculationStatusEnum.WAITING_FOR_START
        assert calculation.created_by_user_id == orm_user.id
        assert calculation.budget_agreement_id == orm_budget_agreement.id

        assert calculation.budget_lhm == budget_lhm
        assert calculation.traffic_lhm == traffic_lhm
        assert calculation.distribution_lhm == distribution_lhm

    def test_create_without_user(self, _repository):
        orm_calc_snapshot = CalculationBudgetSnapshotORMFactory()

        calculation = _repository.create_calculation(
            budget_id=orm_calc_snapshot.budget_id,
            calculation_type=BudgetCalculationTypeEnum.FULL_WITH_TRAFFIC_UPDATE,
            created_by_user_id=None,
            budget_agreement_id=None,
            budget_lhm=None,
            traffic_lhm=None,
            distribution_lhm=None,
        )

        assert calculation.created_by_user_id is None

    def test_create_calculation_while_it_is_already_running(self, _repository):
        orm_calculation = BudgetCalculationORMFactory(
            status=BudgetCalculationStatusEnum.STARTED,
            budget_snapshot__type=BudgetSnapshotTypeEnum.CALCULATION,
        )

        with pytest.raises(BudgetCalculationIsRunning):
            _repository.create_calculation(
                budget_id=orm_calculation.budget_snapshot.budget_id,
                calculation_type=BudgetCalculationTypeEnum.FULL_WITH_TRAFFIC_UPDATE,
                created_by_user_id=None,
                budget_agreement_id=None,
                budget_lhm=None,
                traffic_lhm=None,
                distribution_lhm=None,
            )

    def test_create_without_lhm_vars(self, _repository):
        orm_calc_snapshot = CalculationBudgetSnapshotORMFactory()

        calculation = _repository.create_calculation(
            budget_id=orm_calc_snapshot.budget_id,
            calculation_type=BudgetCalculationTypeEnum.FULL_WITH_TRAFFIC_UPDATE,
            created_by_user_id=None,
            budget_agreement_id=None,
            budget_lhm=None,
            traffic_lhm=None,
            distribution_lhm=None,
        )

        assert calculation.traffic_lhm is None
        assert calculation.distribution_lhm is None

    def test_create_when_budget_was_calculated(self, _repository):
        orm_calculation = BudgetCalculationORMFactory(
            status=BudgetCalculationStatusEnum.CALCULATED,
            budget_snapshot__type=BudgetSnapshotTypeEnum.CALCULATION,
        )

        calculation = _repository.create_calculation(
            budget_id=orm_calculation.budget_snapshot.budget_id,
            calculation_type=BudgetCalculationTypeEnum.FULL_WITH_TRAFFIC_UPDATE,
            created_by_user_id=None,
            budget_agreement_id=None,
            budget_lhm=None,
            traffic_lhm=None,
            distribution_lhm=None,
        )
        assert calculation.status == BudgetCalculationStatusEnum.WAITING_FOR_START


@pytest.mark.django_db
class TestGetCalculationSnapshot:
    def test_ok(self, _repository: BudgetDjangoORMRepository):
        CalculationBudgetSnapshotORMFactory.create_batch(size=3)  # noise snapshots

        orm_calc_snapshot = CalculationBudgetSnapshotORMFactory()

        snapshot = _repository.get_calculation_snapshot(orm_calc_snapshot.budget_id)

        assert isinstance(snapshot, BudgetSnapshot)

        assert snapshot.id == orm_calc_snapshot.id
        assert snapshot.budget_id == orm_calc_snapshot.budget_id
        assert snapshot.name == orm_calc_snapshot.name
        assert snapshot.type == BudgetSnapshotTypeEnum(orm_calc_snapshot.type)
        assert snapshot.created_by_user_id == orm_calc_snapshot.created_by_id


@pytest.mark.django_db
class TestSaveCalculation:
    def test_saved_fields(self, _repository: BudgetDjangoORMRepository):
        orm_calculation = BudgetCalculationORMFactory(
            status=BudgetCalculationStatusEnum.STARTED,
            finished_at=None,
            traffic_synchronized_at=None,
            forecast_rules_applied_at=None,
            agreements_applied_at=None,
            job_id=None,
        )

        calculation = from_orm_calculation_to_domain(
            orm_calculation,
            orm_calculation.budget_snapshot.budget_id,
        )

        status = BudgetCalculationStatusEnum.CALCULATED
        finished_at = datetime(2022, 1, 1, 2, 2, 2, tzinfo=timezone.utc)
        traffic_synchronized_at = datetime(2023, 1, 2, 1, 1, 1, tzinfo=timezone.utc)
        forecast_rules_applied_at = datetime(2023, 1, 2, 1, 1, 1, tzinfo=timezone.utc)
        agreements_applied_at = datetime(2023, 1, 2, 6, 1, 41, tzinfo=timezone.utc)
        job_id = "test-job-id"

        calculation.status = status
        calculation.finished_at = finished_at
        calculation.traffic_synchronized_at = traffic_synchronized_at
        calculation.forecast_rules_applied_at = forecast_rules_applied_at
        calculation.agreements_applied_at = agreements_applied_at
        calculation.job_id = job_id

        _repository.save_calculation(calculation)

        orm_calculation.refresh_from_db()

        assert orm_calculation.status == status.value
        assert orm_calculation.finished_at == finished_at
        assert orm_calculation.traffic_synchronized_at == traffic_synchronized_at
        assert orm_calculation.forecast_rules_applied_at == forecast_rules_applied_at
        assert orm_calculation.agreements_applied_at == agreements_applied_at
        assert orm_calculation.job_id == job_id


@pytest.mark.django_db
class TestGetCalculationById:
    def test_get_when_exists(self, _repository: BudgetDjangoORMRepository):
        orm_calculation = BudgetCalculationORMFactory(
            traffic_synchronized_at=datetime(2023, 1, 2, 1, 1, 1, tzinfo=timezone.utc),
            forecast_rules_applied_at=datetime(2023, 1, 2, 1, 1, 1, tzinfo=timezone.utc),
            agreements_applied_at=datetime(2023, 1, 2, 6, 1, 41, tzinfo=timezone.utc),
            budget_lhm=Month.create_from_year_month(2025, 6),
            traffic_lhm=Month.create_from_year_month(2025, 7),
            distribution_lhm=Month.create_from_year_month(2025, 8),
        )

        calculation = _repository.get_calculation_by_id(orm_calculation.id)

        assert isinstance(calculation, BudgetCalculation)
        assert calculation.id == orm_calculation.id
        assert calculation.traffic_synchronized_at == orm_calculation.traffic_synchronized_at
        assert calculation.forecast_rules_applied_at == orm_calculation.forecast_rules_applied_at
        assert calculation.agreements_applied_at == orm_calculation.agreements_applied_at

        assert calculation.budget_lhm == orm_calculation.budget_lhm
        assert calculation.traffic_lhm == orm_calculation.traffic_lhm
        assert calculation.distribution_lhm == orm_calculation.distribution_lhm

    def test_when_does_not_exist(self, _repository: BudgetDjangoORMRepository):
        with pytest.raises(BudgetCalculationDoesNotExist):
            _repository.get_calculation_by_id(calculation_id=44)


@pytest.mark.django_db
class TestCreateBudget:
    def test_create(self, _repository: BudgetDjangoORMRepository):
        hpmn = OperatorORMFactory()

        lhm = Month.create_from_year_month(2023, 9)

        budget = _repository.create(
            name="test-budget",
            description="test-description",
            period=DatePeriod(date(2022, 11, 1), date(2022, 12, 1)),
            type=BudgetTypeEnum.FROZEN,
            home_operators=[hpmn.id],
            user_id=None,
            last_historical_month=lhm,
        )

        assert isinstance(budget, Budget)

        assert budget.name == "test-budget"
        assert budget.description == "test-description"
        assert budget.period == DatePeriod(date(2022, 11, 1), date(2022, 12, 1))
        assert budget.type == BudgetTypeEnum.FROZEN
        assert budget.home_operators == [hpmn.id]
        assert budget.is_master is False
        assert budget.last_historical_month == lhm

        assert budget.active_snapshot_id is not None
        assert budget.calculation_snapshot_id is not None

        assert budget.forecast_rules_modified_at is None
        assert budget.historical_traffic_modified_at is None

    def test_default_snapshots_are_created(self, _repository: BudgetDjangoORMRepository):
        hpmn = OperatorORMFactory()
        orm_user = UserORMFactory()

        budget = _repository.create(
            name="test-budget",
            period=DatePeriod(date(2022, 11, 1), date(2022, 12, 1)),
            type=BudgetTypeEnum.UPDATED,
            home_operators=[hpmn.id],
            user_id=orm_user.id,
            last_historical_month=Month.create_from_year_month(2022, 12),
            description=None,
        )

        assert budget.active_snapshot_id is not None
        assert budget.calculation_snapshot_id is not None

        orm_budget = models.Budget.objects.get(pk=budget.id)

        assert orm_budget.snapshots.count() == 2

        active_snapshot = orm_budget.snapshots.get(type=BudgetSnapshotTypeEnum.ACTIVE)
        assert active_snapshot.name == BUDGET_SNAPSHOT_SYSTEM_NAME
        assert active_snapshot.created_by == orm_user

        assert budget.active_snapshot_id == active_snapshot.id

        calculation_snapshot = orm_budget.snapshots.get(type=BudgetSnapshotTypeEnum.CALCULATION)
        assert calculation_snapshot.name == BUDGET_SNAPSHOT_SYSTEM_NAME
        assert calculation_snapshot.created_by == orm_user


@pytest.mark.django_db
class TestGetLastFullCalculation:
    def test_when_exist(self, _repository: BudgetDjangoORMRepository):
        orm_snapshot = BudgetSnapshotORMFactory(type=BudgetSnapshotTypeEnum.CALCULATION)

        BudgetCalculationORMFactory(
            budget_snapshot=orm_snapshot,
            type=BudgetCalculationTypeEnum.FULL_WITH_TRAFFIC_UPDATE,
        )
        orm_calculation_without_traffic = BudgetCalculationORMFactory(
            budget_snapshot=orm_snapshot,
            type=BudgetCalculationTypeEnum.FULL_WITHOUT_TRAFFIC_UPDATE,
        )

        calculation = _repository.get_last_full_calculation(orm_snapshot.budget_id)

        assert calculation.id == orm_calculation_without_traffic.id

    def test_when_does_not_exist(self, _repository: BudgetDjangoORMRepository):
        orm_snapshot = BudgetSnapshotORMFactory(type=BudgetSnapshotTypeEnum.CALCULATION)

        with pytest.raises(BudgetCalculationDoesNotExist):
            _repository.get_last_full_calculation(orm_snapshot.budget_id)


@pytest.mark.django_db
class TestGetLastFullTrafficUpdateCalculation:
    def test_when_exists(self, _repository: BudgetDjangoORMRepository):
        orm_snapshot = BudgetSnapshotORMFactory(type=BudgetSnapshotTypeEnum.CALCULATION)

        BudgetCalculationORMFactory(
            budget_snapshot=orm_snapshot,
            type=BudgetCalculationTypeEnum.FULL_WITH_TRAFFIC_UPDATE,
        )

        orm_calculation = BudgetCalculationORMFactory(
            budget_snapshot=orm_snapshot,
            type=BudgetCalculationTypeEnum.FULL_WITH_TRAFFIC_UPDATE,
        )

        BudgetCalculationORMFactory(
            budget_snapshot=orm_snapshot,
            type=BudgetCalculationTypeEnum.FULL_WITHOUT_TRAFFIC_UPDATE,
        )

        calculation = _repository.get_last_full_traffic_update_calculation(orm_snapshot.budget_id)

        assert calculation.id == orm_calculation.id

    def test_when_does_not_exist(self, _repository: BudgetDjangoORMRepository):
        orm_snapshot = BudgetSnapshotORMFactory(type=BudgetSnapshotTypeEnum.CALCULATION)

        with pytest.raises(BudgetCalculationDoesNotExist):
            _repository.get_last_full_traffic_update_calculation(orm_snapshot.budget_id)


@pytest.mark.django_db
class TestGetLastAgreementCalculation:
    def test_when_exists(self, _repository: BudgetDjangoORMRepository):
        orm_snapshot = BudgetSnapshotORMFactory(type=BudgetSnapshotTypeEnum.CALCULATION)
        orm_calculation = BudgetCalculationORMFactory(
            budget_snapshot=orm_snapshot,
            agreement=BudgetAgreementORMFactory(),
            type=BudgetCalculationTypeEnum.SINGLE_AGREEMENT,
        )
        # noise calculation
        BudgetCalculationORMFactory(
            budget_snapshot=orm_snapshot,
            type=BudgetCalculationTypeEnum.FULL_WITH_TRAFFIC_UPDATE,
        )

        calculation = _repository.get_last_agreement_calculation(
            budget_id=orm_snapshot.budget_id,
            budget_agreement_id=orm_calculation.agreement_id,
        )

        assert calculation.id == orm_calculation.id

    def test_when_does_not_exist(self, _repository: BudgetDjangoORMRepository):
        orm_snapshot = BudgetSnapshotORMFactory(type=BudgetSnapshotTypeEnum.CALCULATION)

        with pytest.raises(BudgetCalculationDoesNotExist):
            _repository.get_last_agreement_calculation(budget_id=orm_snapshot.budget_id, budget_agreement_id=542)


@pytest.mark.django_db
class TestGetBySnapshot:
    def test_get_by_active_snapshot_id(self, _repository):
        orm_budget = BudgetORMFactory()

        domain_budget = _repository.get_by_snapshot_id(orm_budget.active_snapshot_id)

        assert domain_budget.id == orm_budget.pk

    def test_get_by_calculation_snapshot_id(self, _repository):
        orm_budget = BudgetORMFactory()

        domain_budget = _repository.get_by_snapshot_id(orm_budget.get_calculation_snapshot_id())

        assert domain_budget.id == orm_budget.pk


@pytest.mark.django_db
class TestDeleteBudget:
    def test_delete_budget_record(self, _repository):
        budget = BudgetORMFactory()

        budget_id = budget.id

        _repository.delete_by_id(budget_id)

        with pytest.raises(BudgetDoesNotExist):
            _repository.get_by_id(budget_id)

    def test_delete_cascade_components(self, _repository):
        budget = BudgetORMFactory()

        BudgetAgreementORMFactory(budget=budget)

        ForecastRuleORMFactory(budget=budget)

        BudgetTrafficRecordORMFactory(budget_snapshot=budget.active_snapshot)

        BudgetCalculationORMFactory(budget_snapshot=budget.active_snapshot)

        assert budget.snapshots.count() > 0
        assert budget.agreements.count() > 0
        assert budget.forecast_rules.count() > 0
        assert budget.active_snapshot.traffic_records.count() > 0

        budget_id = budget.id
        budget_snapshot_id = budget.active_snapshot_id

        _repository.delete_by_id(budget_id)

        assert BudgetAgreement.objects.filter(budget_id=budget_id).exists() is False
        assert ForecastRule.objects.filter(budget_id=budget_id).exists() is False
        assert models.BudgetSnapshot.objects.filter(budget_id=budget_id).exists() is False
        assert models.BudgetCalculation.objects.filter(budget_snapshot_id=budget_snapshot_id).exists() is False
        assert models.BudgetTrafficRecord.objects.filter(budget_snapshot_id=budget_snapshot_id).exists() is False

    def test_set_for_delete(self, _repository):
        budget = BudgetORMFactory(_is_deleting=None)

        _repository.set_for_delete(budget.id)

        budget.refresh_from_db()

        assert budget._is_deleting is True
