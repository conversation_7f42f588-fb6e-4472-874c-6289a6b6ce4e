from datetime import date
from decimal import Decimal

from nga.apps.agreements.enums import DiscountBoundTypeEnum, DiscountCalculationTypeEnum, DiscountDirectionEnum
from nga.apps.iotron.processing.discount_financial_threshold_filler import DiscountFinancialThresholdFiller
from nga.core.enums import ServiceTypeEnum
from nga.core.types import DatePeriod
from tests.apps.agreements.fakes import InMemoryDiscountRepository
from tests.factories.agreements.domain import DiscountFactory, DiscountParameterFactory


class TestDiscountFinancialThresholdFiller:
    def test_fill_financial_threshold_and_above_rate(self):
        """Test that financial threshold and above threshold rate are correctly applied."""
        # Financial Threshold discount (source)
        financial_threshold_discount = DiscountFactory(
            home_operators=[1, 2],
            partner_operators=[3, 4],
            direction=DiscountDirectionEnum.OUTBOUND,
            service_types=[ServiceTypeEnum.DATA],
            period=DatePeriod(date(2024, 7, 1), date(2024, 8, 1)),
            traffic_segments=[100, 200],
            parameters=[
                DiscountParameterFactory(
                    calculation_type=DiscountCalculationTypeEnum.FINANCIAL_THRESHOLD,
                    bound_type=DiscountBoundTypeEnum.FINANCIAL_THRESHOLD,
                    lower_bound=Decimal("5000.00"),
                )
            ],
        )

        # Above Financial Threshold Rate discount (source)
        above_threshold_rate_discount = DiscountFactory(
            home_operators=[1, 2],
            partner_operators=[3, 4],
            direction=DiscountDirectionEnum.OUTBOUND,
            service_types=[ServiceTypeEnum.DATA],
            period=DatePeriod(date(2024, 7, 1), date(2024, 8, 1)),
            traffic_segments=[100, 200],
            parameters=[
                DiscountParameterFactory(
                    calculation_type=DiscountCalculationTypeEnum.STEPPED_TIERED,
                    bound_type=DiscountBoundTypeEnum.FINANCIAL_THRESHOLD,
                    basis_value=Decimal("0.25"),
                )
            ],
        )

        # Target discount (should receive the values)
        target_discount = DiscountFactory(
            home_operators=[1, 2],
            partner_operators=[3, 4],
            direction=DiscountDirectionEnum.OUTBOUND,
            service_types=[ServiceTypeEnum.DATA],
            period=DatePeriod(date(2024, 7, 1), date(2024, 8, 1)),
            traffic_segments=[100, 200],
            parameters=[
                DiscountParameterFactory(
                    calculation_type=DiscountCalculationTypeEnum.SEND_OR_PAY_FINANCIAL,
                )
            ],
        )

        # Repository with all discounts
        discount_repository = InMemoryDiscountRepository([
            financial_threshold_discount,
            above_threshold_rate_discount,
            target_discount,
        ])

        # Create filler and process
        filler = DiscountFinancialThresholdFiller(discount_repository)
        filler.fill_financial_threshold_and_above_rate([
            financial_threshold_discount,
            above_threshold_rate_discount,
            target_discount,
        ])

        # Verify source discounts are deleted
        remaining_discounts = discount_repository.discounts
        assert len(remaining_discounts) == 1
        assert remaining_discounts[0].id == target_discount.id

        # Verify target discount has the values applied
        updated_target = remaining_discounts[0]
        assert updated_target.financial_threshold == Decimal("5000.00")
        assert updated_target.above_financial_threshold_rate == Decimal("0.25")

    def test_no_matching_traffic_no_update(self):
        """Test that discounts with different traffic parameters are not updated."""
        # Financial Threshold discount
        financial_threshold_discount = DiscountFactory(
            home_operators=[1, 2],
            partner_operators=[3, 4],
            direction=DiscountDirectionEnum.OUTBOUND,
            service_types=[ServiceTypeEnum.DATA],
            parameters=[
                DiscountParameterFactory(
                    calculation_type=DiscountCalculationTypeEnum.FINANCIAL_THRESHOLD,
                    bound_type=DiscountBoundTypeEnum.FINANCIAL_THRESHOLD,
                    lower_bound=Decimal("5000.00"),
                )
            ],
        )

        # Target discount with DIFFERENT traffic parameters
        target_discount = DiscountFactory(
            home_operators=[1, 2],
            partner_operators=[3, 4],
            direction=DiscountDirectionEnum.INBOUND,  # Different direction
            service_types=[ServiceTypeEnum.DATA],
            parameters=[
                DiscountParameterFactory(
                    calculation_type=DiscountCalculationTypeEnum.SEND_OR_PAY_FINANCIAL,
                )
            ],
        )

        # Repository with all discounts
        discount_repository = InMemoryDiscountRepository([
            financial_threshold_discount,
            target_discount,
        ])

        # Create filler and process
        filler = DiscountFinancialThresholdFiller(discount_repository)
        filler.fill_financial_threshold_and_above_rate([
            financial_threshold_discount,
            target_discount,
        ])

        # Verify source discount is deleted
        remaining_discounts = discount_repository.discounts
        assert len(remaining_discounts) == 1
        assert remaining_discounts[0].id == target_discount.id

        # Verify target discount was NOT updated (different traffic)
        updated_target = remaining_discounts[0]
        assert updated_target.financial_threshold is None
        assert updated_target.above_financial_threshold_rate is None
