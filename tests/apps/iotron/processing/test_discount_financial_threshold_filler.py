from datetime import date
from decimal import Decimal

from nga.apps.agreements.enums import DiscountBoundTypeEnum, DiscountCalculationTypeEnum, DiscountDirectionEnum
from nga.apps.iotron.processing.discount_financial_threshold_filler import DiscountFinancialThresholdFiller
from nga.core.enums import ServiceTypeEnum
from nga.core.types import DatePeriod
from tests.apps.agreements.fakes import InMemoryDiscountRepository
from tests.factories.agreements.domain import DiscountFactory, DiscountParameterFactory


class TestDiscountFinancialThresholdFiller:
    def test_fill_financial_threshold_and_above_rate(self):
        """Test that financial threshold and above threshold rate are correctly applied."""
        # Financial Threshold discount (source)
        financial_threshold_discount = DiscountFactory(
            home_operators=[1, 2],
            partner_operators=[3, 4],
            direction=DiscountDirectionEnum.OUTBOUND,
            service_types=[ServiceTypeEnum.DATA],
            period=DatePeriod(date(2024, 7, 1), date(2024, 8, 1)),
            traffic_segments=[100, 200],
            parameters=[
                DiscountParameterFactory(
                    calculation_type=DiscountCalculationTypeEnum.FINANCIAL_THRESHOLD,
                    bound_type=DiscountBoundTypeEnum.FINANCIAL_THRESHOLD,
                    lower_bound=Decimal("5000.00"),
                )
            ],
        )

        # Above Financial Threshold Rate discount (source)
        above_threshold_rate_discount = DiscountFactory(
            home_operators=[1, 2],
            partner_operators=[3, 4],
            direction=DiscountDirectionEnum.OUTBOUND,
            service_types=[ServiceTypeEnum.DATA],
            period=DatePeriod(date(2024, 7, 1), date(2024, 8, 1)),
            traffic_segments=[100, 200],
            parameters=[
                DiscountParameterFactory(
                    calculation_type=DiscountCalculationTypeEnum.STEPPED_TIERED,
                    bound_type=DiscountBoundTypeEnum.FINANCIAL_THRESHOLD,
                    basis_value=Decimal("0.25"),
                )
            ],
        )

        # Target discount (should receive the values)
        target_discount = DiscountFactory(
            home_operators=[1, 2],
            partner_operators=[3, 4],
            direction=DiscountDirectionEnum.OUTBOUND,
            service_types=[ServiceTypeEnum.DATA],
            period=DatePeriod(date(2024, 7, 1), date(2024, 8, 1)),
            traffic_segments=[100, 200],
            parameters=[
                DiscountParameterFactory(
                    calculation_type=DiscountCalculationTypeEnum.SEND_OR_PAY_FINANCIAL,
                )
            ],
        )

        # Repository with all discounts
        discount_repository = InMemoryDiscountRepository([
            financial_threshold_discount,
            above_threshold_rate_discount,
            target_discount,
        ])

        # Create filler and process
        filler = DiscountFinancialThresholdFiller(discount_repository)
        filler.fill_financial_threshold_and_above_rate([
            financial_threshold_discount,
            above_threshold_rate_discount,
            target_discount,
        ])

        # Verify source discounts are deleted
        remaining_discounts = discount_repository.discounts
        assert len(remaining_discounts) == 1
        assert remaining_discounts[0].id == target_discount.id

        # Verify target discount has the values applied
        updated_target = remaining_discounts[0]
        assert updated_target.financial_threshold == Decimal("5000.00")
        assert updated_target.above_financial_threshold_rate == Decimal("0.25")

    def test_no_matching_traffic_no_update(self):
        """Test that discounts with different traffic parameters are not updated."""
        # Financial Threshold discount
        financial_threshold_discount = DiscountFactory(
            home_operators=[1, 2],
            partner_operators=[3, 4],
            direction=DiscountDirectionEnum.OUTBOUND,
            service_types=[ServiceTypeEnum.DATA],
            parameters=[
                DiscountParameterFactory(
                    calculation_type=DiscountCalculationTypeEnum.FINANCIAL_THRESHOLD,
                    bound_type=DiscountBoundTypeEnum.FINANCIAL_THRESHOLD,
                    lower_bound=Decimal("5000.00"),
                )
            ],
        )

        # Target discount with DIFFERENT traffic parameters
        target_discount = DiscountFactory(
            home_operators=[1, 2],
            partner_operators=[3, 4],
            direction=DiscountDirectionEnum.INBOUND,  # Different direction
            service_types=[ServiceTypeEnum.DATA],
            parameters=[
                DiscountParameterFactory(
                    calculation_type=DiscountCalculationTypeEnum.SEND_OR_PAY_FINANCIAL,
                )
            ],
        )

        # Repository with all discounts
        discount_repository = InMemoryDiscountRepository([
            financial_threshold_discount,
            target_discount,
        ])

        # Create filler and process
        filler = DiscountFinancialThresholdFiller(discount_repository)
        filler.fill_financial_threshold_and_above_rate([
            financial_threshold_discount,
            target_discount,
        ])

        # Verify source discount is deleted
        remaining_discounts = discount_repository.discounts
        assert len(remaining_discounts) == 1
        assert remaining_discounts[0].id == target_discount.id

        # Verify target discount was NOT updated (different traffic)
        updated_target = remaining_discounts[0]
        assert updated_target.financial_threshold is None
        assert updated_target.above_financial_threshold_rate is None

    def test_above_threshold_rate_only_applied_to_send_or_pay_financial(self):
        """Test that above threshold rate is only applied to Send or Pay Financial discounts."""
        # Above Financial Threshold Rate discount (source)
        above_threshold_rate_discount = DiscountFactory(
            home_operators=[1, 2],
            partner_operators=[3, 4],
            direction=DiscountDirectionEnum.OUTBOUND,
            service_types=[ServiceTypeEnum.DATA],
            period=DatePeriod(date(2024, 7, 1), date(2024, 8, 1)),
            parameters=[
                DiscountParameterFactory(
                    calculation_type=DiscountCalculationTypeEnum.STEPPED_TIERED,
                    bound_type=DiscountBoundTypeEnum.FINANCIAL_THRESHOLD,
                    basis_value=Decimal("0.25"),
                )
            ],
        )

        # Send or Pay Financial discount (should receive the rate)
        sop_financial_discount = DiscountFactory(
            home_operators=[1, 2],
            partner_operators=[3, 4],
            direction=DiscountDirectionEnum.OUTBOUND,
            service_types=[ServiceTypeEnum.DATA],
            period=DatePeriod(date(2024, 7, 1), date(2024, 8, 1)),
            parameters=[
                DiscountParameterFactory(
                    calculation_type=DiscountCalculationTypeEnum.SEND_OR_PAY_FINANCIAL,
                )
            ],
        )

        # Single Rate Effective discount (should NOT receive the rate)
        sre_discount = DiscountFactory(
            home_operators=[1, 2],
            partner_operators=[3, 4],
            direction=DiscountDirectionEnum.OUTBOUND,
            service_types=[ServiceTypeEnum.DATA],
            period=DatePeriod(date(2024, 7, 1), date(2024, 8, 1)),
            parameters=[
                DiscountParameterFactory(
                    calculation_type=DiscountCalculationTypeEnum.SINGLE_RATE_EFFECTIVE,
                )
            ],
        )

        # Repository with all discounts
        discount_repository = InMemoryDiscountRepository([
            above_threshold_rate_discount,
            sop_financial_discount,
            sre_discount,
        ])

        # Create filler and process
        filler = DiscountFinancialThresholdFiller(discount_repository)
        filler.fill_financial_threshold_and_above_rate([
            above_threshold_rate_discount,
            sop_financial_discount,
            sre_discount,
        ])

        # Verify source discount is deleted
        remaining_discounts = discount_repository.discounts
        assert len(remaining_discounts) == 2

        # Find the remaining discounts
        sop_financial_updated = next(d for d in remaining_discounts if d.id == sop_financial_discount.id)
        sre_updated = next(d for d in remaining_discounts if d.id == sre_discount.id)

        # Verify only Send or Pay Financial discount received the above threshold rate
        assert sop_financial_updated.above_financial_threshold_rate == Decimal("0.25")
        assert sre_updated.above_financial_threshold_rate is None

    def test_financial_threshold_applied_to_all_matching_discounts(self):
        """Test that financial threshold is applied to all matching discounts, not just Send or Pay Financial."""
        # Financial Threshold discount (source)
        financial_threshold_discount = DiscountFactory(
            home_operators=[1, 2],
            partner_operators=[3, 4],
            direction=DiscountDirectionEnum.OUTBOUND,
            service_types=[ServiceTypeEnum.DATA],
            period=DatePeriod(date(2024, 7, 1), date(2024, 8, 1)),
            parameters=[
                DiscountParameterFactory(
                    calculation_type=DiscountCalculationTypeEnum.FINANCIAL_THRESHOLD,
                    bound_type=DiscountBoundTypeEnum.FINANCIAL_THRESHOLD,
                    lower_bound=Decimal("5000.00"),
                )
            ],
        )

        # Send or Pay Financial discount
        sop_financial_discount = DiscountFactory(
            home_operators=[1, 2],
            partner_operators=[3, 4],
            direction=DiscountDirectionEnum.OUTBOUND,
            service_types=[ServiceTypeEnum.DATA],
            period=DatePeriod(date(2024, 7, 1), date(2024, 8, 1)),
            parameters=[
                DiscountParameterFactory(
                    calculation_type=DiscountCalculationTypeEnum.SEND_OR_PAY_FINANCIAL,
                )
            ],
        )

        # Single Rate Effective discount
        sre_discount = DiscountFactory(
            home_operators=[1, 2],
            partner_operators=[3, 4],
            direction=DiscountDirectionEnum.OUTBOUND,
            service_types=[ServiceTypeEnum.DATA],
            period=DatePeriod(date(2024, 7, 1), date(2024, 8, 1)),
            parameters=[
                DiscountParameterFactory(
                    calculation_type=DiscountCalculationTypeEnum.SINGLE_RATE_EFFECTIVE,
                )
            ],
        )

        # Repository with all discounts
        discount_repository = InMemoryDiscountRepository([
            financial_threshold_discount,
            sop_financial_discount,
            sre_discount,
        ])

        # Create filler and process
        filler = DiscountFinancialThresholdFiller(discount_repository)
        filler.fill_financial_threshold_and_above_rate([
            financial_threshold_discount,
            sop_financial_discount,
            sre_discount,
        ])

        # Verify source discount is deleted
        remaining_discounts = discount_repository.discounts
        assert len(remaining_discounts) == 2

        # Find the remaining discounts
        sop_financial_updated = next(d for d in remaining_discounts if d.id == sop_financial_discount.id)
        sre_updated = next(d for d in remaining_discounts if d.id == sre_discount.id)

        # Verify both discounts received the financial threshold
        assert sop_financial_updated.financial_threshold == Decimal("5000.00")
        assert sre_updated.financial_threshold == Decimal("5000.00")
