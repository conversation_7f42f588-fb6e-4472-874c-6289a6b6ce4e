import pytest

from nga.apps.budgets.domain.exceptions import BudgetCalculationError
from nga.apps.budgets.enums import BudgetCalculationStatusEnum, BudgetCalculationTypeEnum
from nga.apps.calculation.budgets import BudgetCalculationFactory, BudgetCalculationOptions
from tests.apps.budgets.fakes import InMemoryBudgetRepository
from tests.factories.budgets import BudgetFactory, MasterBudgetFactory


class TestBudgetCalculationFactory:
    def test_create_calculation_record(self):
        budget = BudgetFactory()
        options = BudgetCalculationOptions(
            user_id=55,
            calculation_type=BudgetCalculationTypeEnum.FULL_WITH_TRAFFIC_UPDATE,
            budget_agreement_id=None,
            budget_lhm=None,
            traffic_lhm=None,
            distribution_lhm=None,
        )

        budget_repository = InMemoryBudgetRepository(budgets=[budget])

        factory = BudgetCalculationFactory(budget_repository)

        calculation = factory.create(budget, options)

        assert calculation.budget_id == budget.id
        assert calculation.type == options.calculation_type
        assert calculation.status == BudgetCalculationStatusEnum.WAITING_FOR_START
        assert calculation.budget_snapshot_id == budget.calculation_snapshot_id
        assert calculation.budget_agreement_id is None
        assert calculation.created_by_user_id == options.user_id

    @pytest.mark.parametrize(
        "calculation_type",
        [
            BudgetCalculationTypeEnum.FULL_WITHOUT_TRAFFIC_UPDATE,
            BudgetCalculationTypeEnum.ONLY_MODIFIED_AGREEMENTS,
            BudgetCalculationTypeEnum.SINGLE_AGREEMENT,
        ],
    )
    def test_master_budget_calculation_is_allowed_with_full_type_only(
        self,
        calculation_type: BudgetCalculationTypeEnum,
    ):
        budget = MasterBudgetFactory()
        budget_repository = InMemoryBudgetRepository(budgets=[budget])

        options = BudgetCalculationOptions(
            user_id=55,
            calculation_type=calculation_type,
            budget_agreement_id=None,
            budget_lhm=None,
            traffic_lhm=None,
            distribution_lhm=None,
        )

        factory = BudgetCalculationFactory(budget_repository)

        with pytest.raises(BudgetCalculationError) as exc_info:
            factory.create(budget, options)

        assert "FULL" in exc_info.value.message

    def test_calculation_for_single_agreement(self):
        budget = BudgetFactory()
        budget_repository = InMemoryBudgetRepository(budgets=[budget])

        factory = BudgetCalculationFactory(budget_repository)

        options = BudgetCalculationOptions(
            calculation_type=BudgetCalculationTypeEnum.SINGLE_AGREEMENT,
            budget_agreement_id=55,
            user_id=None,
            budget_lhm=None,
            traffic_lhm=None,
            distribution_lhm=None,
        )

        calculation = factory.create(budget, options)

        assert calculation.type == options.calculation_type
        assert calculation.budget_agreement_id == options.budget_agreement_id

    def test_agreement_id_is_required_for_single_agreement_calculation(self):
        budget = BudgetFactory()
        budget_repository = InMemoryBudgetRepository(budgets=[budget])

        factory = BudgetCalculationFactory(budget_repository)

        options = BudgetCalculationOptions(
            calculation_type=BudgetCalculationTypeEnum.SINGLE_AGREEMENT,
            budget_agreement_id=None,
            user_id=None,
            budget_lhm=None,
            traffic_lhm=None,
            distribution_lhm=None,
        )

        with pytest.raises(BudgetCalculationError) as exc_info:
            factory.create(budget, options)

        assert "budget_agreement_id" in exc_info.value.message
