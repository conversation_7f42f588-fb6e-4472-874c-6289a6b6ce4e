"""
Django settings for app project.

Generated by 'django-admin startproject' using Django 4.1.1.

For more information on this file, see
https://docs.djangoproject.com/en/4.1/topics/settings/

For the full list of settings and their values, see
https://docs.djangoproject.com/en/4.1/ref/settings/
"""

import os
import sys
from pathlib import Path

import environ
from corsheaders.defaults import default_headers
from django.urls import reverse_lazy

from nga.apps.import_export_data import consts as import_export_consts
from nga.apps.import_export_data import resource_getters
from nga.core.config import AppEnv

APPLICATION_TITLE = "Budget Simulations API"

# Build paths inside the project like this: BASE_DIR / 'subdir'.
BASE_DIR = Path(__file__).resolve().parent.parent
TEST_WITH_PYTEST = "pytest" in sys.modules
ENV_FILENAME = "test.env" if TEST_WITH_PYTEST else ".env"

env = environ.Env()
env.read_env(BASE_DIR / ENV_FILENAME)

# Quick-start development settings - unsuitable for production
# See https://docs.djangoproject.com/en/4.1/howto/deployment/checklist/

# SECURITY WARNING: keep the secret key used in production secret!
SECRET_KEY = env("SECRET_KEY")

# SECURITY WARNING: don't run with debug turned on in production!
DEBUG = env.bool("DEBUG")

ALLOWED_HOSTS = ["*"]
CSRF_TRUSTED_ORIGINS = env.list("CSRF_TRUSTED_ORIGINS")

# Application definition

INSTALLED_APPS = [
    "django.contrib.auth",
    "django.contrib.contenttypes",
    "django.contrib.sessions",
    "django.contrib.messages",
    "django.contrib.staticfiles",
    # third-party
    "corsheaders",
    "rest_framework",
    "drf_yasg",
    "mozilla_django_oidc",
    "storages",
    "import_export",
    "import_export_celery",
    "rangefilter",
    "health_check",
    "health_check.db",
    "health_check.contrib.migrations",
    "more_admin_filters",
    "admin_auto_filters",
    "django_filters",
    # nga apps
    "nga.apps.users",
    "nga.apps.admin_site.apps.NGAAdminConfig",
    "nga.apps.references",
    "nga.apps.budgets",
    "nga.apps.budget_background_jobs",
    "nga.apps.forecasts",
    "nga.apps.agreements",
    "nga.apps.import_export_data",
    "nga.apps.qa",
    "nga.apps.chatbot",
    "nga.apps.iotron",
    "nga.apps.iot_rates",
    "nga.infra.health_checks",
]

MIDDLEWARE = [
    "corsheaders.middleware.CorsMiddleware",
    "django.middleware.security.SecurityMiddleware",
    "django.contrib.sessions.middleware.SessionMiddleware",
    "django.middleware.common.CommonMiddleware",
    "django.middleware.csrf.CsrfViewMiddleware",
    "django.contrib.auth.middleware.AuthenticationMiddleware",
    "django.contrib.messages.middleware.MessageMiddleware",
    "django.middleware.clickjacking.XFrameOptionsMiddleware",
    # django_import_export middleware
    "author.middlewares.AuthorDefaultBackendMiddleware",
]

ROOT_URLCONF = "nga.infra.urls"

ADMIN_TEMPLATES_DIR = os.path.join(BASE_DIR, "apps", "admin_site", "templates")
TEMPLATES = [
    {
        "BACKEND": "django.template.backends.django.DjangoTemplates",
        "DIRS": [ADMIN_TEMPLATES_DIR],
        "APP_DIRS": True,
        "OPTIONS": {
            "context_processors": [
                "django.template.context_processors.debug",
                "django.template.context_processors.request",
                "django.contrib.auth.context_processors.auth",
                "django.contrib.messages.context_processors.messages",
            ],
        },
    },
]

WSGI_APPLICATION = "nga.infra.wsgi.application"

DATA_UPLOAD_MAX_NUMBER_FIELDS = 15000

# Database
# https://docs.djangoproject.com/en/4.1/ref/settings/#databases

DB_STATEMENT_TIMEOUT_SEC = env.int("DB_STATEMENT_TIMEOUT_SEC", 300)

DEFAULT_DB_CONF = env.db()

DEFAULT_DB_CONF["OPTIONS"] = {
    "options": f"-c statement_timeout={DB_STATEMENT_TIMEOUT_SEC * 1000}",
}

DATABASES = {"default": DEFAULT_DB_CONF}

# Migrations

MIGRATION_MODULES = {
    "traffic": "nga.apps.traffic.infra.orm.migrations",
    "budgets": "nga.apps.budgets.infra.orm.migrations",
    "forecasts": "nga.apps.forecasts.infra.orm.migrations",
    "agreements": "nga.apps.agreements.infra.orm.migrations",
    "references": "nga.apps.references.infra.orm.migrations",
    "iotron": "nga.apps.iotron.infra.orm.migrations",
}

# Password validation
# https://docs.djangoproject.com/en/4.1/ref/settings/#auth-password-validators

AUTH_PASSWORD_VALIDATORS = [
    {
        "NAME": "django.contrib.auth.password_validation.UserAttributeSimilarityValidator",  # noqa
    },
    {
        "NAME": "django.contrib.auth.password_validation.MinimumLengthValidator",  # noqa
    },
    {
        "NAME": "django.contrib.auth.password_validation.CommonPasswordValidator",  # noqa
    },
    {
        "NAME": "django.contrib.auth.password_validation.NumericPasswordValidator",  # noqa
    },
]

# Internationalization
# https://docs.djangoproject.com/en/4.1/topics/i18n/

LANGUAGE_CODE = "en-us"

TIME_ZONE = "UTC"

USE_I18N = False

USE_TZ = True

# Static files (CSS, JavaScript, Images)
# https://docs.djangoproject.com/en/4.1/howto/static-files/

# Static and Media settings

USE_S3 = env.bool("USE_S3", False)

if USE_S3:
    AWS_ACCESS_KEY_ID = env.str("S3_KEY_ID")
    AWS_SECRET_ACCESS_KEY = env.str("S3_SECRET_KEY")
    AWS_STORAGE_BUCKET_NAME = env.str("S3_BUCKET_NAME")
    AWS_DEFAULT_ACL = "public-read"
    AWS_S3_OBJECT_PARAMETERS = {
        "CacheControl": "max-age=86400",
    }

    S3_REGION = env.str("S3_REGION")
    S3_NAMESPACE = env.str("S3_NAMESPACE")
    _base_domain = f"objectstorage.{S3_REGION}.oraclecloud.com"

    AWS_S3_ENDPOINT_URL = f"https://{S3_NAMESPACE}.compat.{_base_domain}/"
    AWS_S3_CUSTOM_DOMAIN = f"{_base_domain}/n/{S3_NAMESPACE}/b/{AWS_STORAGE_BUCKET_NAME}/o"

    STORAGES = {
        "default": {
            "BACKEND": "nga.infra.storage.NGAMediaStorage",
        },
        "staticfiles": {
            "BACKEND": "nga.infra.storage.NGAStaticStorage",
        },
    }

    STATIC_URL = AWS_S3_ENDPOINT_URL
    MEDIA_URL = AWS_S3_ENDPOINT_URL
else:
    STATIC_URL = "static/"
    STATIC_ROOT = BASE_DIR / "static/"

    MEDIA_URL = "media/"
    MEDIA_ROOT = BASE_DIR / "media/"

# django_import_export settings
IMPORT_EXPORT_TMP_STORAGE_CLASS = "import_export.tmp_storages.MediaStorage"

IMPORT_EXPORT_CELERY_INIT_MODULE = "nga.infra"

IMPORT_EXPORT_CELERY_MODELS = {
    import_export_consts.MONTHLY_AGGREGATED_TRAFFIC_RECORD_IOTRON: {
        "app_label": "references",
        "model_name": "MonthlyAggregatedTrafficRecord",
        "resource": resource_getters.get_monthly_aggregated_traffic_record_resource,
    },
    import_export_consts.MONTHLY_AGGREGATED_TRAFFIC_RECORD_MFS: {
        "app_label": "references",
        "model_name": "MonthlyAggregatedTrafficRecord",
        "resource": resource_getters.get_monthly_aggregated_traffic_record_mfs_resource,
    },
    import_export_consts.MONTHLY_AGGREGATED_TRAFFIC_RECORD_SYNIVERSE: {
        "app_label": "references",
        "model_name": "MonthlyAggregatedTrafficRecord",
        "resource": resource_getters.get_monthly_aggregated_traffic_record_syniverse_resource,
    },
    import_export_consts.EXTERNAL_CALCULATED_TRAFFIC_RECORD: {
        "app_label": "references",
        "model_name": "ExternalCalculatedTrafficRecord",
        "resource": resource_getters.get_master_budget_traffic_record_resource,
    },
    import_export_consts.FORECAST_RULE_RECORD: {
        "app_label": "forecasts",
        "model_name": "ForecastRule",
        "resource": resource_getters.get_forecast_rule_resource,
    },
    import_export_consts.EXTERNAL_AGREEMENTS_RECORD: {
        "app_label": "iotron",
        "model_name": "ExternalAgreement",
        "resource": resource_getters.get_external_agreement_resource,
    },
    import_export_consts.IOT_RATE_RECORD: {
        "app_label": "iot_rates",
        "model_name": "IOTRate",
        "resource": resource_getters.get_iot_rate_resource,
    },
    import_export_consts.COUNTRY_RECORD: {
        "app_label": "references",
        "model_name": "Country",
        "resource": resource_getters.get_country_resource,
    },
    import_export_consts.COUNTRY_GROUP_RECORD: {
        "app_label": "references",
        "model_name": "CountryGroup",
        "resource": resource_getters.get_country_group_resource,
    },
    import_export_consts.COUNTRY_PHONE_CODE_RECORD: {
        "app_label": "references",
        "model_name": "CountryPhoneCode",
        "resource": resource_getters.get_country_phone_code_resource,
    },
    import_export_consts.OPERATOR_RECORD: {
        "app_label": "references",
        "model_name": "Operator",
        "resource": resource_getters.get_operator_resource,
    },
    import_export_consts.EU_OPERATOR_RECORD: {
        "app_label": "references",
        "model_name": "EUOperator",
        "resource": resource_getters.get_eu_operator_resource,
    },
    import_export_consts.EXCHANGE_RATE_RECORD: {
        "app_label": "references",
        "model_name": "ExchangeRate",
        "resource": resource_getters.get_exchange_rate_resource,
    },
    import_export_consts.TRAFFIC_SEGMENT_RECORD: {
        "app_label": "references",
        "model_name": "TrafficSegment",
        "resource": resource_getters.get_traffic_segment_resource,
    },
    import_export_consts.IMSI_COUNT_RECORD: {
        "app_label": "references",
        "model_name": "IMSICountRecord",
        "resource": resource_getters.get_imsi_count_resource,
    },
    import_export_consts.IMSI_COUNT_RECORD_ASYNC: {
        "app_label": "references",
        "model_name": "IMSICountRecord",
        "resource": resource_getters.get_imsi_count_resource,
    },
}

# Default primary key field type
# https://docs.djangoproject.com/en/4.1/ref/settings/#default-auto-field

DEFAULT_AUTO_FIELD = "django.db.models.BigAutoField"

# REST Framework Settings
REST_FRAMEWORK = {
    "DEFAULT_RENDERER_CLASSES": [
        "rest_framework.renderers.JSONRenderer",
    ],
    "DEFAULT_PARSER_CLASSES": [
        "rest_framework.parsers.JSONParser",
    ],
    "DEFAULT_AUTHENTICATION_CLASSES": [
        "mozilla_django_oidc.contrib.drf.OIDCAuthentication",
    ],
    "TEST_REQUEST_DEFAULT_FORMAT": "json",
}

# OIDC Configuration
OIDC_RP_CLIENT_ID = env("OIDC_CLIENT_ID")
OIDC_RP_CLIENT_SECRET = env("OIDC_CLIENT_SECRET")
OIDC_OP_AUTHORIZATION_ENDPOINT = env("OIDC_AUTHORIZATION_URL")
OIDC_OP_TOKEN_ENDPOINT = env("OIDC_TOKEN_URL")
OIDC_OP_USER_ENDPOINT = None  # Will not be used
OIDC_OP_TOKEN_INTROSPECTION_ENDPOINT = env("OIDC_TOKEN_INTROSPECTION_URL")
OIDC_OP_JWKS_ENDPOINT = env("OIDC_JWKS_URL")
OIDC_OP_LOGOUT_URL = env("OIDC_LOGOUT_URL")
OIDC_RP_SCOPES = "openid email profile"
OIDC_RP_SIGN_ALGO = "RS256"

OIDC_CREATE_USER = True
OIDC_STORE_ACCESS_TOKEN = True
OIDC_DRF_AUTH_BACKEND = "nga.apps.users.backends.PlatformAuthenticationBackend"

# IOTRON Configuration
IOTRON_API_URL = env("IOTRON_API_URL")
IOTRON_CLIENT_ID = env("IOTRON_CLIENT_ID")
IOTRON_CLIENT_SECRET = env("IOTRON_CLIENT_SECRET")

# Connected Platform Configuration
CONNECTED_PLATFORM_API_URL = env("CONNECTED_PLATFORM_API_URL")
CONNECTED_PLATFORM_CLIENT_ID = env("CONNECTED_PLATFORM_CLIENT_ID")
CONNECTED_PLATFORM_CLIENT_SECRET = env("CONNECTED_PLATFORM_CLIENT_SECRET")
CONNECTED_PLATFORM_OIDC_CLIENT_NAME = env("CONNECTED_PLATFORM_OIDC_CLIENT_NAME")
CONNECTED_PLATFORM_APPROLE_NAME = env("CONNECTED_PLATFORM_APPROLE_NAME")

# Django auth settings
AUTH_USER_MODEL = "users.User"
AUTHENTICATION_BACKENDS = [OIDC_DRF_AUTH_BACKEND]
LOGIN_URL = reverse_lazy("oidc_authentication_callback")

# In order to disable redirect to non-existing /accounts/profile URL
# when ?next is empty. By default, it will use "/"
LOGIN_REDIRECT_URL = None

# CORS
CORS_ALLOWED_ORIGINS = env.list("CORS_ALLOWED_ORIGINS")
CORS_ALLOW_CREDENTIALS = True
CORS_ALLOW_HEADERS = list(default_headers) + [
    "cache-control",
]

# Swagger
SWAGGER_SETTINGS = {
    "USE_SESSION_AUTH": False,
    "SECURITY_DEFINITIONS": {
        f"{APPLICATION_TITLE} - Swagger": {
            "type": "oauth2",
            "authorizationUrl": OIDC_OP_AUTHORIZATION_ENDPOINT,
            "tokenUrl": OIDC_OP_TOKEN_ENDPOINT,
            "flow": "accessCode",
            "scopes": {
                OIDC_RP_SCOPES: OIDC_RP_SCOPES,
            },
        }
    },
    "OAUTH2_CONFIG": {
        "appName": APPLICATION_TITLE,
    },
    "DEEP_LINKING": True,  # adds URL fragments #test-url
    "DEFAULT_FIELD_INSPECTORS": [
        # Custom extra field inspectors
        "nga.infra.swagger.inspectors.MultiCollectionFormatFieldInspector",
        "drf_yasg.inspectors.CamelCaseJSONFilter",
        "drf_yasg.inspectors.RecursiveFieldInspector",
        "drf_yasg.inspectors.ReferencingSerializerInspector",
        "nga.infra.swagger.inspectors.EnumFieldInspector",  # Overriden field inspector
        "drf_yasg.inspectors.FileFieldInspector",
        "drf_yasg.inspectors.DictFieldInspector",
        "drf_yasg.inspectors.JSONFieldInspector",
        "drf_yasg.inspectors.HiddenFieldInspector",
        "drf_yasg.inspectors.RelatedFieldInspector",
        "drf_yasg.inspectors.SerializerMethodFieldInspector",
        "drf_yasg.inspectors.SimpleFieldInspector",
        "drf_yasg.inspectors.StringDefaultFieldInspector",
    ],
    "DEFAULT_AUTO_SCHEMA_CLASS": "nga.infra.swagger.auto_schema.NGASwaggerAutoSchema",
}

# Setup support for proxy headers
SECURE_PROXY_SSL_HEADER = ("HTTP_X_FORWARDED_PROTO", "https")

LOGGING = {
    "version": 1,
    "disable_existing_loggers": False,
    "formatters": {
        "default": {
            "format": "[%(asctime)s] %(levelname)s [%(name)s] [%(process)d] [%(filename)s:%(lineno)d] - %(message)s",
            "datefmt": "%Y-%m-%d %H:%M:%S",
        }
    },
    "loggers": {
        "root": {"level": "INFO", "handlers": ["console"]},
        "gunicorn.access": {
            "level": "INFO",
            "handlers": ["console"],
            "qualname": "gunicorn.access",
            "propagate": False,
        },
        "gunicorn.error": {
            "level": "INFO",
            "handlers": ["console"],
            "qualname": "gunicorn.error",
            "propagate": False,
        },
        "uvicorn.access": {
            "level": "INFO",
            "handlers": ["console"],
            "qualname": "uvicorn.access",
            "propagate": False,
        },
        "uvicorn.error": {
            "level": "INFO",
            "handlers": ["console"],
            "qualname": "uvicorn.error",
            "propagate": False,
        },
        "mozilla_django_oidc": {
            "handlers": ["console"],
            "level": "INFO",
        },
        "celery": {
            "handlers": ["console"],
            "level": "INFO",
        },
    },
    "handlers": {
        "console": {
            "class": "logging.StreamHandler",
            "formatter": "default",
            "stream": "ext://sys.stdout",
        }
    },
}

XLSX_CONTENT_TYPE = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"

# ORM settings
DEFAULT_BATCH_SIZE = 5000

# Redis
REDIS_HOST = env("REDIS_HOST")
REDIS_PORT = env.int("REDIS_PORT")

# CELERY
CELERY_BROKER_URL = env("CELERY_BROKER_URL")
CELERY_TASK_DEFAULT_QUEUE = env("CELERY_TASK_DEFAULT_QUEUE", default="nga-default-queue")
CELERY_RESULT_BACKEND = f"redis://{REDIS_HOST}:{REDIS_PORT}/0"
CELERYD_HIJACK_ROOT_LOGGER = False

# django-health-check
BROKER_URL = CELERY_BROKER_URL  # TODO: drop after NGA-805
REDIS_URL = f"redis://{REDIS_HOST}:{REDIS_PORT}/0"

APP_ENV: AppEnv = AppEnv(env("APP_ENV", default="local"))

# TODO: NGA-714, uncomment to enable auto-synchronization of records with IOTRON
# if APP_ENV in [AppEnv.STAGE, AppEnv.PROD]:
#     CELERY_BEAT_SCHEDULE = {
#         "sync_historical_monthly_aggregations": {
#             "task": "nga.apps.import_export_data.tasks.periodic_data_sync.sync_historical_monthly_aggregations",
#             "schedule": crontab(minute=0, hour=0, day_of_month=2),
#         },
#         "sync_master_budget_traffic_records": {
#             "task": "nga.apps.import_export_data.tasks.periodic_data_sync.load_master_budget_calculation_results",
#             "schedule": crontab(minute=0, hour=0, day_of_month=2),
#         },
#         "sync_agreements": {
#             "task": "nga.apps.import_export_data.tasks.periodic_data_sync.sync_agreements",
#             "schedule": crontab(minute=0, hour=0, day_of_month=2),
#         },
#         "sync_countries": {
#             "task": "nga.apps.import_export_data.tasks.periodic_data_sync.sync_countries",
#             "schedule": crontab(minute=0, hour=0, day_of_month=2),
#         },
#         "sync_operators": {
#             "task": "nga.apps.import_export_data.tasks.periodic_data_sync.sync_operators",
#             "schedule": crontab(minute=0, hour=0, day_of_month=2),
#         },
#         "sync_exchange_rate": {
#             "task": "nga.apps.import_export_data.tasks.periodic_data_sync.sync_exchange_rate",
#             "schedule": crontab(minute=0, hour=0, day_of_month=2),
#         },
#         "sync_traffic_segments": {
#             "task": "nga.apps.import_export_data.tasks.periodic_data_sync.sync_traffic_segments",
#             "schedule": crontab(minute=0, hour=0, day_of_month=2),
#         },
#         "sync_imsi_count": {
#             "task": "nga.apps.import_export_data.tasks.periodic_data_sync.sync_imsi_count",
#             "schedule": crontab(minute=0, hour=0, day_of_month=2),
#         },
#     }

CELERY_BEAT_SCHEDULE = {
    "start_scheduled_budget_background_jobs": {
        "task": "nga.apps.budget_background_jobs.tasks.start_scheduled_budget_background_jobs",
        "schedule": env.int("BUDGET_BACKGROUND_JOBS_START_INTERVAL_SECONDS"),
    }
}

# Daphne server
ASGI_APPLICATION = "nga.infra.asgi.application"

# django-channels
CHANNEL_LAYERS = {
    "default": {
        "BACKEND": "channels_redis.core.RedisChannelLayer",
        "CONFIG": {
            "hosts": [(REDIS_HOST, REDIS_PORT)],
        },
    },
}

# django-silk
ENABLE_DJANGO_SILK = env.bool("ENABLE_DJANGO_SILK", default=False)

if ENABLE_DJANGO_SILK is True and APP_ENV in (AppEnv.LOCAL, APP_ENV.DEV):
    INSTALLED_APPS.append("silk")
    MIDDLEWARE.append("silk.middleware.SilkyMiddleware")

    SILKY_PYTHON_PROFILER = False
    SILKY_PYTHON_PROFILER_BINARY = True

# Chatbot OpenAI settings
OPEN_AI_API_KEY = env("OPEN_AI_API_KEY")
OPEN_AI_MODEL = env("OPEN_AI_MODEL")
