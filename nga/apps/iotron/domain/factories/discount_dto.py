from datetime import datetime
from decimal import Decimal

# Type alias for traffic key tuple
TrafficKey = tuple[tuple[int, ...], tuple[int, ...], str, tuple[str, ...], str, str, tuple[int, ...] | None]

from nga.apps.agreements.consts import FULL_DATE_FORMAT
from nga.apps.agreements.domain.dto import DiscountDTO, DiscountParameterDTO
from nga.apps.agreements.domain.value_objects import DiscountQualifyingRule
from nga.apps.agreements.enums import (
    DiscountBalancingEnum,
    DiscountBasisEnum,
    DiscountBoundTypeEnum,
    DiscountCalculationTypeEnum,
    DiscountDirectionEnum,
    DiscountQualifyingBasisEnum,
    DiscountSettlementMethodEnum,
    TaxTypeEnum,
)
from nga.apps.iotron.domain.dto import DiscountDict
from nga.apps.iotron.domain.utils import to_decimal
from nga.core.enums import CallDestinationEnum, IMSICountTypeEnum, ServiceTypeEnum, VolumeTypeEnum
from nga.core.types import Month


class DiscountDTOFactory:
    @classmethod
    def create_from_external_discount(
        cls,
        discount: DiscountDict,
        include_access_fee_in_sop_financial_inbound: bool,
        include_access_fee_in_sop_financial_outbound: bool,
    ) -> DiscountDTO:
        discount_dto = cls._from_discount_dict_to_discount_dto(discount)

        discount_dto = cls._adjust_if_model_is_pmpi_with_incremental_charging(discount_dto)

        discount_dto = cls._adjust_if_model_is_pmpi_stepped_tiered(discount_dto)

        discount_dto = cls._adjust_if_model_sop_financial(
            discount_dto,
            include_access_fee_in_sop_financial_inbound,
            include_access_fee_in_sop_financial_outbound,
        )

        return discount_dto

    @classmethod
    def is_financial_threshold_discount(cls, discount: DiscountDict) -> bool:
        """Check if discount is a Financial Threshold discount."""
        if not discount["discount_parameters"]:
            return False

        # Check if any parameter has both FINANCIAL_THRESHOLD calculation_type and bound_type
        for param in discount["discount_parameters"]:
            if (param.get("calculation_type") == DiscountCalculationTypeEnum.FINANCIAL_THRESHOLD.name and
                param.get("bound_type") == DiscountBoundTypeEnum.FINANCIAL_THRESHOLD.name):
                return True
        return False

    @classmethod
    def extract_financial_thresholds(
        cls,
        discounts: list[DiscountDict],
        include_access_fee_in_sop_financial_inbound: bool = False,
        include_access_fee_in_sop_financial_outbound: bool = False,
    ) -> dict[TrafficKey, Decimal]:
        """
        Extract financial threshold values from Financial Threshold discounts.

        Args:
            discounts: List of discount dictionaries
            include_access_fee_in_sop_financial_inbound: Whether to include ACCESS_FEE in inbound SoP Financial discounts
            include_access_fee_in_sop_financial_outbound: Whether to include ACCESS_FEE in outbound SoP Financial discounts

        Returns:
            A mapping from traffic parameters to financial threshold value.
            Traffic parameters tuple: (home_operators, partner_operators, direction, service_types, start_date, end_date, traffic_segments)
        """
        financial_thresholds = {}

        for discount in discounts:
            if not cls.is_financial_threshold_discount(discount):
                continue

            # Normalize service types based on "Include Access Fee" parameters
            normalized_service_types = cls._normalize_service_types_for_traffic_key(
                discount["service_types"],
                discount["direction"],
                include_access_fee_in_sop_financial_inbound,
                include_access_fee_in_sop_financial_outbound
            )

            # Convert dates to Month objects and then to consistent string format
            from datetime import datetime
            from nga.apps.agreements.consts import FULL_DATE_FORMAT
            from nga.core.types import Month

            start_month = Month.create_from_date(datetime.strptime(discount["start_date"], FULL_DATE_FORMAT))
            end_month = Month.create_from_date(datetime.strptime(discount["end_date"], FULL_DATE_FORMAT))

            # Create traffic key for matching
            traffic_key = (
                tuple(sorted(discount["home_operators"])),
                tuple(sorted(discount["partner_operators"])),
                discount["direction"],
                tuple(sorted(normalized_service_types)),
                start_month.to_date().strftime("%Y-%m-%d"),
                end_month.to_date().strftime("%Y-%m-%d"),
                tuple(sorted(discount["traffic_segments"])) if discount["traffic_segments"] else None
            )

            # Extract the lower_bound value from the Financial Threshold parameter
            for param in discount["discount_parameters"]:
                if (param.get("calculation_type") == DiscountCalculationTypeEnum.FINANCIAL_THRESHOLD.name and
                    param.get("bound_type") == DiscountBoundTypeEnum.FINANCIAL_THRESHOLD.name and
                    param.get("lower_bound")):

                    threshold_value = to_decimal(param["lower_bound"])
                    if threshold_value is not None:
                        financial_thresholds[traffic_key] = threshold_value
                    break

        return financial_thresholds

    @classmethod
    def _normalize_service_types_for_traffic_key(
        cls,
        service_types: list[str],
        direction: str,
        include_access_fee_in_sop_financial_inbound: bool,
        include_access_fee_in_sop_financial_outbound: bool
    ) -> list[str]:
        """
        Normalize service types for traffic key matching based on "Include Access Fee" parameters.

        If the "Include Access Fee" parameter is True for the direction, ACCESS_FEE should be included.
        If it's False, ACCESS_FEE should be excluded from the traffic key.
        """
        from nga.core.enums import ServiceTypeEnum

        # Determine if ACCESS_FEE should be included based on direction and parameters
        if direction == DiscountDirectionEnum.INBOUND.name:
            should_include_access_fee = include_access_fee_in_sop_financial_inbound
        elif direction == DiscountDirectionEnum.OUTBOUND.name:
            should_include_access_fee = include_access_fee_in_sop_financial_outbound
        else:  # BIDIRECTIONAL
            should_include_access_fee = (
                include_access_fee_in_sop_financial_inbound or include_access_fee_in_sop_financial_outbound
            )

        normalized_service_types = list(service_types)

        if should_include_access_fee:
            # Ensure ACCESS_FEE is included
            if ServiceTypeEnum.ACCESS_FEE.name not in normalized_service_types:
                normalized_service_types.append(ServiceTypeEnum.ACCESS_FEE.name)
        else:
            # Ensure ACCESS_FEE is excluded
            normalized_service_types = [
                st for st in normalized_service_types if st != ServiceTypeEnum.ACCESS_FEE.name
            ]

        return normalized_service_types

    @classmethod
    def apply_financial_threshold_if_sop_financial(
        cls,
        discount_dto: DiscountDTO,
        financial_thresholds: dict[TrafficKey, Decimal],
        include_access_fee_in_sop_financial_inbound: bool = False,
        include_access_fee_in_sop_financial_outbound: bool = False,
    ) -> DiscountDTO:
        """Apply financial threshold to Send or Pay Financial discounts."""

        # Check if this is a Send or Pay Financial discount
        if not any(p.calculation_type == DiscountCalculationTypeEnum.SEND_OR_PAY_FINANCIAL
                  for p in discount_dto.parameters):
            return discount_dto

        # If financial threshold is already set (from same discount), don't override it
        if discount_dto.financial_threshold is not None:
            return discount_dto

        # Normalize service types based on "Include Access Fee" parameters
        service_type_names = [st.name for st in discount_dto.service_types]
        normalized_service_types = cls._normalize_service_types_for_traffic_key(
            service_type_names,
            discount_dto.direction.name,
            include_access_fee_in_sop_financial_inbound,
            include_access_fee_in_sop_financial_outbound
        )

        # Create traffic key for this discount
        traffic_key = (
            tuple(sorted(discount_dto.home_operators)),
            tuple(sorted(discount_dto.partner_operators)),
            discount_dto.direction.name,
            tuple(sorted(normalized_service_types)),
            discount_dto.start_date.to_date().strftime("%Y-%m-%d"),
            discount_dto.end_date.to_date().strftime("%Y-%m-%d"),
            tuple(sorted(discount_dto.traffic_segments)) if discount_dto.traffic_segments else None
        )

        # Apply financial threshold if found
        if traffic_key in financial_thresholds:
            # Create a new DiscountDTO with the financial threshold applied
            return DiscountDTO(
                home_operators=discount_dto.home_operators,
                partner_operators=discount_dto.partner_operators,
                direction=discount_dto.direction,
                service_types=discount_dto.service_types,
                start_date=discount_dto.start_date,
                end_date=discount_dto.end_date,
                model_type=discount_dto.model_type,
                currency_code=discount_dto.currency_code,
                tax_type=discount_dto.tax_type,
                volume_type=discount_dto.volume_type,
                settlement_method=discount_dto.settlement_method,
                call_destinations=discount_dto.call_destinations,
                called_countries=discount_dto.called_countries,
                traffic_segments=discount_dto.traffic_segments,
                imsi_count_type=discount_dto.imsi_count_type,
                qualifying_rule=discount_dto.qualifying_rule,
                parameters=discount_dto.parameters,
                inbound_market_share=discount_dto.inbound_market_share,
                commitment_distribution_parameters=discount_dto.commitment_distribution_parameters,
                above_commitment_rate=discount_dto.above_commitment_rate,
                sub_discounts=discount_dto.sub_discounts,
                financial_threshold=financial_thresholds[traffic_key],  # Apply the threshold
                above_financial_threshold_rate=discount_dto.above_financial_threshold_rate,
            )

        return discount_dto
        
    @classmethod
    def _adjust_if_model_is_pmpi_with_incremental_charging(cls, discount_dto: DiscountDTO) -> DiscountDTO:
        """
        If discount parameters contain parameter that fits PMPI with incremental charging discount model, then we need
        to manually set imsi count type to DATA and append ACCESS_FEE service type.
        """

        if len(discount_dto.parameters) != 1:
            return discount_dto

        p = discount_dto.parameters[0]

        if p.calculation_type != DiscountCalculationTypeEnum.PER_MONTH_PER_IMSI_WITH_INCREMENTAL_CHARGING:
            return discount_dto

        if ServiceTypeEnum.ACCESS_FEE not in discount_dto.service_types:
            discount_dto.service_types = tuple((*discount_dto.service_types, ServiceTypeEnum.ACCESS_FEE))

        if discount_dto.imsi_count_type is None:
            discount_dto.imsi_count_type = IMSICountTypeEnum.DATA

        return discount_dto

    @classmethod
    def _adjust_if_model_is_pmpi_stepped_tiered(cls, discount_dto: DiscountDTO) -> DiscountDTO:
        """
        If discount parameters contain parameter that fit PMPI Stepped Tiered model, then we need to set manually
        imsi count type to DATA.
        """

        calculation_types_are_valid = any(
            p.calculation_type == DiscountCalculationTypeEnum.PER_MONTH_PER_IMSI_STEPPED_TIERED
            for p in discount_dto.parameters
        )

        if not calculation_types_are_valid:
            return discount_dto

        if discount_dto.imsi_count_type is None:
            discount_dto.imsi_count_type = IMSICountTypeEnum.DATA

        return discount_dto

    @classmethod
    def _adjust_if_model_sop_financial(
        cls,
        discount_dto: DiscountDTO,
        include_access_fee_in_sop_financial_inbound: bool,
        include_access_fee_in_sop_financial_outbound: bool,
    ) -> DiscountDTO:
        """
        If discount parameters contain parameter that fit Send or Pay Financial model, then we need to include/exclude
        manually Access Fee service type.
        """

        if len(discount_dto.parameters) != 1:
            return discount_dto

        p = discount_dto.parameters[0]

        if p.calculation_type != DiscountCalculationTypeEnum.SEND_OR_PAY_FINANCIAL:
            return discount_dto

        if discount_dto.direction == DiscountDirectionEnum.INBOUND:
            should_adjust_access_fee = include_access_fee_in_sop_financial_inbound

        elif discount_dto.direction == DiscountDirectionEnum.OUTBOUND:
            should_adjust_access_fee = include_access_fee_in_sop_financial_outbound

        else:
            should_adjust_access_fee = (
                include_access_fee_in_sop_financial_inbound or include_access_fee_in_sop_financial_outbound
            )

        if should_adjust_access_fee and ServiceTypeEnum.ACCESS_FEE not in discount_dto.service_types:
            discount_dto.service_types = tuple((*discount_dto.service_types, ServiceTypeEnum.ACCESS_FEE))

        elif (
            not should_adjust_access_fee
            and ServiceTypeEnum.ACCESS_FEE in discount_dto.service_types
            and len(discount_dto.service_types) > 1
        ):
            discount_dto.service_types = tuple(
                st for st in discount_dto.service_types if st != ServiceTypeEnum.ACCESS_FEE
            )

        return discount_dto

    @classmethod
    def _from_discount_dict_to_discount_dto(cls, discount: DiscountDict) -> DiscountDTO:
        call_destinations = None
        if _cds := discount.get("call_destinations"):
            call_destinations = tuple(CallDestinationEnum[cd] for cd in _cds)

        qualifying_direction = None
        if _q_direction := discount.get("qualifying_direction"):
            qualifying_direction = DiscountDirectionEnum[_q_direction]

        qualifying_service_types = None
        if _q_service_types := discount.get("qualifying_service_types"):
            qualifying_service_types = tuple(ServiceTypeEnum[st] for st in _q_service_types)

        qualifying_basis = None
        if _q_qualifying_basis := discount.get("qualifying_basis"):
            qualifying_basis = DiscountQualifyingBasisEnum[_q_qualifying_basis]

        qualifying_lower_bound = to_decimal(discount.get("qualifying_lower_bound"))

        if (
            qualifying_direction is not None
            and qualifying_service_types is not None
            and qualifying_basis is not None
            and qualifying_lower_bound is not None
        ):
            qualifying_rule = DiscountQualifyingRule(
                direction=qualifying_direction,
                service_types=qualifying_service_types,
                basis=qualifying_basis,
                volume_type=VolumeTypeEnum[discount["volume_type"]],
                lower_bound=qualifying_lower_bound,
                upper_bound=None,
            )
        else:
            qualifying_rule = None

        parameters = [
            DiscountParameterDTO(
                calculation_type=DiscountCalculationTypeEnum[p["calculation_type"]],
                basis=DiscountBasisEnum[p["basis"]] if p.get("basis", "") else None,  # type: ignore[misc]
                basis_value=to_decimal(p["basis_value"]),
                balancing=DiscountBalancingEnum[p["balancing"]] if p.get("balancing") else None,  # type: ignore[misc]
                bound_type=(
                    DiscountBoundTypeEnum[p["bound_type"]] if p.get("bound_type") else None  # type: ignore[misc]
                ),
                lower_bound=to_decimal(p["lower_bound"]),
                upper_bound=to_decimal(p["upper_bound"]),
                toll_rate=to_decimal(p["toll_rate"]),
                airtime_rate=to_decimal(p["airtime_rate"]),
                fair_usage_rate=to_decimal(p["fair_usage_rate"]),
                fair_usage_threshold=to_decimal(p["fair_usage_threshold"]),
                access_fee_rate=to_decimal(p["access_fee_rate"]),
                incremental_rate=to_decimal(p["incremental_rate"]),
            )
            for p in discount["discount_parameters"]
        ]

        parameters = sorted(parameters, key=lambda p: (p.lower_bound is not None, p.lower_bound))

        # Extract financial threshold value if this discount has Send or Pay Financial calculation type
        financial_threshold = None
        has_sop_financial = any(
            DiscountCalculationTypeEnum[p["calculation_type"]] == DiscountCalculationTypeEnum.SEND_OR_PAY_FINANCIAL
            for p in discount["discount_parameters"]
        )

        if has_sop_financial:
            # Look for Financial Threshold parameter to get the lower_bound value
            for param in discount["discount_parameters"]:
                if (param.get("calculation_type") == DiscountCalculationTypeEnum.FINANCIAL_THRESHOLD.name and
                    param.get("bound_type") == DiscountBoundTypeEnum.FINANCIAL_THRESHOLD.name and
                    param.get("lower_bound")):

                    financial_threshold = to_decimal(param["lower_bound"])
                    break

        return DiscountDTO(
            home_operators=tuple(discount["home_operators"]),
            partner_operators=tuple(discount["partner_operators"]),
            direction=DiscountDirectionEnum[discount["direction"]],
            service_types=tuple(ServiceTypeEnum[service_type] for service_type in discount["service_types"]),
            start_date=Month.create_from_date(datetime.strptime(discount["start_date"], FULL_DATE_FORMAT)),
            end_date=Month.create_from_date(datetime.strptime(discount["end_date"], FULL_DATE_FORMAT)),
            currency_code=discount["currency_code"],
            tax_type=TaxTypeEnum[discount["tax_type"]],
            volume_type=VolumeTypeEnum[discount["volume_type"]],
            settlement_method=DiscountSettlementMethodEnum[discount["settlement_method"]],
            call_destinations=call_destinations,
            called_countries=tuple(discount["called_countries"]) if discount["called_countries"] else None,
            traffic_segments=tuple(discount["traffic_segments"]) if discount["traffic_segments"] else None,
            imsi_count_type=IMSICountTypeEnum[discount["imsi_count_type"]] if discount["imsi_count_type"] else None,
            qualifying_rule=qualifying_rule,
            parameters=tuple(parameters),
            model_type=None,
            inbound_market_share=None,
            above_commitment_rate=None,
            commitment_distribution_parameters=None,  # TODO: https://nextgenclearing.atlassian.net/browse/SNB-2406
            financial_threshold=financial_threshold,  # Set from Financial Threshold parameter if available
            above_financial_threshold_rate=None,
        )
