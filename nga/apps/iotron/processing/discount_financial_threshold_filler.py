from datetime import date
from decimal import Decimal
from typing import Optional

# Type alias for traffic key tuple
TrafficKey = tuple[tuple[int, ...], tuple[int, ...], str, tuple[str, ...], date, date, tuple[int, ...] | None]

from nga.apps.agreements.domain.discount_model_type import evaluate_discount_model_type
from nga.apps.agreements.domain.mappers import from_discount_parameter_to_dto
from nga.apps.agreements.domain.models import Discount
from nga.apps.agreements.domain.repositories import AbstractDiscountRepository
from nga.apps.agreements.enums import DiscountBoundTypeEnum, DiscountCalculationTypeEnum, DiscountDirectionEnum
from nga.core.enums import ServiceTypeEnum


class DiscountFinancialThresholdFiller:
    def __init__(self, discount_repository: AbstractDiscountRepository) -> None:
        self._discount_repository = discount_repository

    def fill_financial_threshold_and_above_rate(
        self,
        discounts: list[Discount],
        include_access_fee_in_sop_financial_inbound: bool = False,
        include_access_fee_in_sop_financial_outbound: bool = False,
    ) -> None:
        """
        Fill financial threshold and above financial threshold rate for discounts.
        
        This method processes all discounts to:
        1. Extract financial threshold values from Financial Threshold discounts
        2. Extract above financial threshold rates from Above Financial Threshold Rate discounts  
        3. Apply these values to matching Send or Pay Financial discounts
        4. Delete the source discounts that were used for extraction
        """
        # Extract financial threshold values and above threshold rates
        financial_thresholds = self._extract_financial_thresholds(
            discounts, include_access_fee_in_sop_financial_inbound, include_access_fee_in_sop_financial_outbound
        )
        above_threshold_rates = self._extract_above_financial_threshold_rates(
            discounts, include_access_fee_in_sop_financial_inbound, include_access_fee_in_sop_financial_outbound
        )
        
        # Track discounts to delete
        discount_ids_for_delete = []
        
        # Process each discount
        for discount in discounts:
            # Check if this is a source discount that should be deleted
            if (self._is_financial_threshold_discount(discount) or 
                self._is_above_financial_threshold_rate_discount(discount)):
                discount_ids_for_delete.append(discount.id)
                continue
            
            # Apply financial threshold and above threshold rate to matching discounts
            self._apply_financial_threshold_and_above_rate(
                discount, financial_thresholds, above_threshold_rates,
                include_access_fee_in_sop_financial_inbound, include_access_fee_in_sop_financial_outbound
            )
        
        # Delete source discounts
        for discount_id in discount_ids_for_delete:
            self._discount_repository.delete_by_id(discount_id)

    def _is_financial_threshold_discount(self, discount: Discount) -> bool:
        """Check if discount is a Financial Threshold discount."""
        return any(
            param.calculation_type == DiscountCalculationTypeEnum.FINANCIAL_THRESHOLD and
            param.bound_type == DiscountBoundTypeEnum.FINANCIAL_THRESHOLD
            for param in discount.parameters
        )

    def _is_above_financial_threshold_rate_discount(self, discount: Discount) -> bool:
        """Check if discount is an Above Financial Threshold Rate discount."""
        return any(
            param.calculation_type == DiscountCalculationTypeEnum.STEPPED_TIERED and
            param.bound_type == DiscountBoundTypeEnum.FINANCIAL_THRESHOLD
            for param in discount.parameters
        )

    def _extract_financial_thresholds(
        self,
        discounts: list[Discount],
        include_access_fee_in_sop_financial_inbound: bool,
        include_access_fee_in_sop_financial_outbound: bool,
    ) -> dict[TrafficKey, Decimal]:
        """Extract financial threshold values from Financial Threshold discounts."""
        financial_thresholds = {}
        
        for discount in discounts:
            if not self._is_financial_threshold_discount(discount):
                continue
                
            traffic_key = self._create_traffic_key(
                discount, include_access_fee_in_sop_financial_inbound, include_access_fee_in_sop_financial_outbound
            )
            
            # Extract the lower_bound value from the Financial Threshold parameter
            for param in discount.parameters:
                if (param.calculation_type == DiscountCalculationTypeEnum.FINANCIAL_THRESHOLD and
                    param.bound_type == DiscountBoundTypeEnum.FINANCIAL_THRESHOLD and
                    param.lower_bound is not None):
                    
                    financial_thresholds[traffic_key] = param.lower_bound
                    break
        
        return financial_thresholds

    def _extract_above_financial_threshold_rates(
        self,
        discounts: list[Discount],
        include_access_fee_in_sop_financial_inbound: bool,
        include_access_fee_in_sop_financial_outbound: bool,
    ) -> dict[TrafficKey, Decimal]:
        """Extract above financial threshold rates from Above Financial Threshold Rate discounts."""
        above_threshold_rates = {}
        
        for discount in discounts:
            if not self._is_above_financial_threshold_rate_discount(discount):
                continue
                
            traffic_key = self._create_traffic_key(
                discount, include_access_fee_in_sop_financial_inbound, include_access_fee_in_sop_financial_outbound
            )
            
            # Extract the basis_value from the Above Financial Threshold Rate parameter
            for param in discount.parameters:
                if (param.calculation_type == DiscountCalculationTypeEnum.STEPPED_TIERED and
                    param.bound_type == DiscountBoundTypeEnum.FINANCIAL_THRESHOLD and
                    param.basis_value is not None):
                    
                    above_threshold_rates[traffic_key] = param.basis_value
                    break
        
        return above_threshold_rates

    def _create_traffic_key(
        self,
        discount: Discount,
        include_access_fee_in_sop_financial_inbound: bool,
        include_access_fee_in_sop_financial_outbound: bool,
    ) -> TrafficKey:
        """Create a traffic key for matching discounts."""
        # Normalize service types based on "Include Access Fee" parameters
        normalized_service_types = self._normalize_service_types_for_traffic_key(
            [st.name for st in discount.service_types],
            discount.direction.name,
            include_access_fee_in_sop_financial_inbound,
            include_access_fee_in_sop_financial_outbound
        )

        return (
            tuple(sorted(discount.home_operators)),
            tuple(sorted(discount.partner_operators)),
            discount.direction.name,
            tuple(sorted(normalized_service_types)),
            discount.period.start_date,
            discount.period.end_date,
            tuple(sorted(discount.traffic_segments)) if discount.traffic_segments else None
        )

    def _normalize_service_types_for_traffic_key(
        self,
        service_types: list[str],
        direction: str,
        include_access_fee_in_sop_financial_inbound: bool,
        include_access_fee_in_sop_financial_outbound: bool
    ) -> list[str]:
        """
        Normalize service types for traffic key matching based on "Include Access Fee" parameters.

        If the "Include Access Fee" parameter is True for the direction, ACCESS_FEE should be included.
        If it's False, ACCESS_FEE should be excluded from the traffic key.
        """
        # Determine if ACCESS_FEE should be included based on direction and parameters
        if direction == DiscountDirectionEnum.INBOUND.name:
            should_include_access_fee = include_access_fee_in_sop_financial_inbound
        elif direction == DiscountDirectionEnum.OUTBOUND.name:
            should_include_access_fee = include_access_fee_in_sop_financial_outbound
        else:  # BIDIRECTIONAL
            should_include_access_fee = (
                include_access_fee_in_sop_financial_inbound or include_access_fee_in_sop_financial_outbound
            )

        normalized_service_types = list(service_types)

        if should_include_access_fee:
            # Ensure ACCESS_FEE is included
            if ServiceTypeEnum.ACCESS_FEE.name not in normalized_service_types:
                normalized_service_types.append(ServiceTypeEnum.ACCESS_FEE.name)
        else:
            # Ensure ACCESS_FEE is excluded
            normalized_service_types = [
                st for st in normalized_service_types if st != ServiceTypeEnum.ACCESS_FEE.name
            ]

        return normalized_service_types

    def _apply_financial_threshold_and_above_rate(
        self,
        discount: Discount,
        financial_thresholds: dict[TrafficKey, Decimal],
        above_threshold_rates: dict[TrafficKey, Decimal],
        include_access_fee_in_sop_financial_inbound: bool,
        include_access_fee_in_sop_financial_outbound: bool,
    ) -> None:
        """Apply financial threshold and above threshold rate to a discount if matching traffic is found."""
        traffic_key = self._create_traffic_key(
            discount, include_access_fee_in_sop_financial_inbound, include_access_fee_in_sop_financial_outbound
        )
        
        # Check if we need to update this discount
        financial_threshold = financial_thresholds.get(traffic_key)
        above_threshold_rate = above_threshold_rates.get(traffic_key)
        
        if financial_threshold is not None or above_threshold_rate is not None:
            # Update the discount
            if financial_threshold is not None:
                discount.financial_threshold = financial_threshold
            if above_threshold_rate is not None:
                discount.above_financial_threshold_rate = above_threshold_rate
                
            # Save the updated discount
            self._discount_repository.save(discount)
